<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
	xmlns:tools="http://schemas.android.com/tools"
	android:layout_width="match_parent"
	android:layout_height="wrap_content"
	android:orientation="horizontal"
	android:gravity="center_vertical"
	android:padding="8dp">

	<!-- 播放按钮 -->
	<ImageButton
		android:id="@+id/playButton"
		android:layout_width="48dp"
		android:layout_height="48dp"
		android:layout_marginEnd="12dp"
		android:background="?attr/selectableItemBackgroundBorderless"
		android:src="@drawable/ic_play_arrow_24dp"
		android:contentDescription="@string/voice_message_play"
		android:scaleType="centerInside"
		tools:ignore="TouchTargetSizeCheck" />

	<!-- 加载进度条 -->
	<ProgressBar
		android:id="@+id/loadingProgress"
		android:layout_width="48dp"
		android:layout_height="48dp"
		android:layout_marginEnd="12dp"
		android:visibility="gone"
		style="?android:attr/progressBarStyle" />

	<!-- 波形和进度区域 -->
	<LinearLayout
		android:layout_width="0dp"
		android:layout_height="wrap_content"
		android:layout_weight="1"
		android:orientation="vertical">

		<!-- 波形视图（可选，这里用SeekBar代替） -->
		<SeekBar
			android:id="@+id/progressSeekBar"
			android:layout_width="match_parent"
			android:layout_height="wrap_content"
			android:layout_marginBottom="4dp"
			android:max="100"
			android:progress="0" />

		<!-- 时长显示 -->
		<TextView
			android:id="@+id/durationText"
			android:layout_width="wrap_content"
			android:layout_height="wrap_content"
			android:layout_gravity="end"
			android:textSize="12sp"
			android:textAppearance="?android:attr/textAppearanceSmall"
			tools:text="0:00 / 1:23" />

	</LinearLayout>

</LinearLayout>
