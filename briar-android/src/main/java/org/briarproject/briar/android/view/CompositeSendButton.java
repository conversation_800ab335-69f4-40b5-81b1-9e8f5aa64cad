package org.briarproject.briar.android.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;

import org.briarproject.briar.R;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatImageButton;

import static android.content.Context.LAYOUT_INFLATER_SERVICE;
import static java.util.Objects.requireNonNull;

public class CompositeSendButton extends FrameLayout {

	private final AppCompatImageButton sendButton, imageButton, voiceButton;
	private final ImageView bombBadge;
	private final ProgressBar progressBar;

	private boolean hasImageSupport = false;
	private boolean hasVoiceSupport = false;

	public CompositeSendButton(@NonNull Context context,
			@Nullable AttributeSet attrs) {
		super(context, attrs);
		LayoutInflater inflater = (LayoutInflater) requireNonNull(
				context.getSystemService(LAYOUT_INFLATER_SERVICE));
		inflater.inflate(R.layout.view_composite_send_button, this, true);

		sendButton = findViewById(R.id.sendButton);
		imageButton = findViewById(R.id.imageButton);
		voiceButton = findViewById(R.id.voiceButton);
		bombBadge = findViewById(R.id.bombBadge);
		progressBar = findViewById(R.id.progressBar);
	}

	@Override
	public void setEnabled(boolean enabled) {
		setSendEnabled(enabled);
	}

	@Override
	public void setOnClickListener(@Nullable View.OnClickListener l) {
		setOnSendClickListener(l);
	}

	public void setOnSendClickListener(@Nullable OnClickListener l) {
		sendButton.setOnClickListener(l);
	}

	public void setSendEnabled(boolean enabled) {
		sendButton.setEnabled(enabled);
	}

	public void setOnImageClickListener(@Nullable OnClickListener l) {
		imageButton.setOnClickListener(l);
	}

	public void setOnVoiceClickListener(@Nullable OnClickListener l) {
		voiceButton.setOnClickListener(l);
	}

	/**
	 * By default, image support is disabled.
	 * Once you know that it is supported in the current context,
	 * call this method to enable it.
	 */
	public void setImagesSupported() {
		hasImageSupport = true;
		imageButton.setImageResource(R.drawable.ic_image);
	}

	/**
	 * By default, voice support is disabled.
	 * Once you know that it is supported in the current context,
	 * call this method to enable it.
	 */
	public void setVoiceSupported() {
		hasVoiceSupport = true;
		voiceButton.setImageResource(R.drawable.ic_mic_24dp);
	}

	public boolean hasImageSupport() {
		return hasImageSupport;
	}

	public boolean hasVoiceSupport() {
		return hasVoiceSupport;
	}

	public void setBombVisible(boolean visible) {
		bombBadge.setVisibility(visible ? VISIBLE : INVISIBLE);
	}

	public void showImageButton(boolean showImageButton, boolean sendEnabled) {
		showAttachmentButtons(showImageButton, sendEnabled, false);
	}

	public void showVoiceButton(boolean showVoiceButton, boolean sendEnabled) {
		showAttachmentButtons(false, sendEnabled, showVoiceButton);
	}

	public void showAttachmentButtons(boolean showImageButton, boolean sendEnabled, boolean showVoiceButton) {
		if (showImageButton || showVoiceButton) {
			// 显示附件按钮，隐藏发送按钮
			if (showImageButton) {
				imageButton.setVisibility(VISIBLE);
				imageButton.setEnabled(true);
			} else {
				imageButton.setVisibility(INVISIBLE);
				imageButton.setEnabled(false);
			}

			if (showVoiceButton) {
				voiceButton.setVisibility(VISIBLE);
				voiceButton.setEnabled(true);
			} else {
				voiceButton.setVisibility(INVISIBLE);
				voiceButton.setEnabled(false);
			}

			sendButton.setEnabled(false);
			sendButton.setVisibility(INVISIBLE);
		} else {
			// 显示发送按钮，隐藏附件按钮
			sendButton.setVisibility(VISIBLE);
			sendButton.setEnabled(sendEnabled);

			imageButton.setVisibility(INVISIBLE);
			imageButton.setEnabled(false);

			voiceButton.setVisibility(INVISIBLE);
			voiceButton.setEnabled(false);
		}
	}

	public void showProgress(boolean show) {
		sendButton.setVisibility(show ? INVISIBLE : VISIBLE);
		imageButton.setVisibility(show ? INVISIBLE : VISIBLE);
		voiceButton.setVisibility(show ? INVISIBLE : VISIBLE);
		progressBar.setVisibility(show ? VISIBLE : INVISIBLE);
	}

}
