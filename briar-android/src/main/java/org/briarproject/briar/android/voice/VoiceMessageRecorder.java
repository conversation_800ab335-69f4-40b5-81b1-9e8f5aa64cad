package org.briarproject.briar.android.voice;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.media.MediaRecorder;
import android.os.Handler;
import android.os.Looper;

import org.briarproject.nullsafety.NotNullByDefault;

import java.io.File;
import java.io.IOException;
import java.util.logging.Logger;

import androidx.annotation.Nullable;
import androidx.annotation.UiThread;
import androidx.core.content.ContextCompat;

import static java.util.logging.Level.WARNING;
import static org.briarproject.briar.api.attachment.MediaConstants.MAX_VOICE_MESSAGE_DURATION_MS;

/**
 * 语音消息录制器
 * 
 * 负责录制语音消息，包括权限检查、录制控制和文件管理。
 */
@UiThread
@NotNullByDefault
public class VoiceMessageRecorder {

	private static final Logger LOG = Logger.getLogger(VoiceMessageRecorder.class.getName());
	
	private final Context context;
	private final VoiceRecorderListener listener;
	private final Handler handler = new Handler(Looper.getMainLooper());
	
	@Nullable
	private MediaRecorder mediaRecorder;
	@Nullable
	private File outputFile;
	private long recordingStartTime;
	private boolean isRecording = false;
	
	// 录制进度更新间隔（毫秒）
	private static final int PROGRESS_UPDATE_INTERVAL = 100;
	
	public VoiceMessageRecorder(Context context, VoiceRecorderListener listener) {
		this.context = context;
		this.listener = listener;
	}
	
	/**
	 * 检查是否有录音权限
	 */
	public boolean hasRecordPermission() {
		return ContextCompat.checkSelfPermission(context, 
			Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED;
	}
	
	/**
	 * 开始录制语音消息
	 * 
	 * @param outputFile 输出文件
	 * @return 是否成功开始录制
	 */
	public boolean startRecording(File outputFile) {
		if (!hasRecordPermission()) {
			listener.onRecordingError("没有录音权限");
			return false;
		}
		
		if (isRecording) {
			LOG.warning("Already recording, ignoring start request");
			return false;
		}
		
		try {
			this.outputFile = outputFile;
			mediaRecorder = new MediaRecorder();
			
			// 配置录制参数
			mediaRecorder.setAudioSource(MediaRecorder.AudioSource.MIC);
			mediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.AAC_ADTS);
			mediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);
			mediaRecorder.setAudioSamplingRate(44100);
			mediaRecorder.setAudioEncodingBitRate(96000);
			mediaRecorder.setOutputFile(outputFile.getAbsolutePath());
			
			// 设置最大录制时长
			mediaRecorder.setMaxDuration((int) MAX_VOICE_MESSAGE_DURATION_MS);
			mediaRecorder.setOnInfoListener((mr, what, extra) -> {
				if (what == MediaRecorder.MEDIA_RECORDER_INFO_MAX_DURATION_REACHED) {
					handler.post(() -> {
						stopRecording();
						listener.onMaxDurationReached();
					});
				}
			});
			
			mediaRecorder.prepare();
			mediaRecorder.start();
			
			recordingStartTime = System.currentTimeMillis();
			isRecording = true;
			
			// 开始进度更新
			startProgressUpdates();
			
			listener.onRecordingStarted();
			LOG.info("Voice recording started");
			return true;
			
		} catch (IOException e) {
			LOG.log(WARNING, "Failed to start recording", e);
			cleanup();
			listener.onRecordingError("录制启动失败: " + e.getMessage());
			return false;
		}
	}
	
	/**
	 * 停止录制
	 * 
	 * @return 录制的文件，如果录制失败则返回null
	 */
	@Nullable
	public File stopRecording() {
		if (!isRecording || mediaRecorder == null) {
			return null;
		}
		
		try {
			mediaRecorder.stop();
			long duration = System.currentTimeMillis() - recordingStartTime;
			
			File result = outputFile;
			cleanup();
			
			if (result != null && result.exists() && result.length() > 0) {
				listener.onRecordingCompleted(result, duration);
				LOG.info("Voice recording completed, duration: " + duration + "ms");
				return result;
			} else {
				listener.onRecordingError("录制文件无效");
				return null;
			}
			
		} catch (RuntimeException e) {
			LOG.log(WARNING, "Failed to stop recording", e);
			cleanup();
			listener.onRecordingError("录制停止失败: " + e.getMessage());
			return null;
		}
	}
	
	/**
	 * 取消录制
	 */
	public void cancelRecording() {
		if (!isRecording) {
			return;
		}
		
		cleanup();
		
		// 删除录制文件
		if (outputFile != null && outputFile.exists()) {
			if (!outputFile.delete()) {
				LOG.warning("Failed to delete cancelled recording file");
			}
		}
		
		listener.onRecordingCancelled();
		LOG.info("Voice recording cancelled");
	}
	
	/**
	 * 获取当前录制时长（毫秒）
	 */
	public long getCurrentDuration() {
		if (!isRecording) {
			return 0;
		}
		return System.currentTimeMillis() - recordingStartTime;
	}
	
	/**
	 * 是否正在录制
	 */
	public boolean isRecording() {
		return isRecording;
	}
	
	/**
	 * 获取最大录制时长剩余时间（毫秒）
	 */
	public long getRemainingTime() {
		if (!isRecording) {
			return MAX_VOICE_MESSAGE_DURATION_MS;
		}
		long elapsed = getCurrentDuration();
		return Math.max(0, MAX_VOICE_MESSAGE_DURATION_MS - elapsed);
	}
	
	/**
	 * 开始进度更新
	 */
	private void startProgressUpdates() {
		handler.post(new Runnable() {
			@Override
			public void run() {
				if (isRecording) {
					long duration = getCurrentDuration();
					long remaining = getRemainingTime();
					listener.onRecordingProgress(duration, remaining);
					
					// 继续更新
					handler.postDelayed(this, PROGRESS_UPDATE_INTERVAL);
				}
			}
		});
	}
	
	/**
	 * 清理资源
	 */
	private void cleanup() {
		isRecording = false;
		
		if (mediaRecorder != null) {
			try {
				mediaRecorder.release();
			} catch (RuntimeException e) {
				LOG.log(WARNING, "Error releasing MediaRecorder", e);
			}
			mediaRecorder = null;
		}
		
		outputFile = null;
		
		// 停止进度更新
		handler.removeCallbacksAndMessages(null);
	}
	
	/**
	 * 释放录制器资源
	 */
	public void release() {
		if (isRecording) {
			cancelRecording();
		}
		cleanup();
	}
	
	/**
	 * 语音录制监听器接口
	 */
	public interface VoiceRecorderListener {
		
		/**
		 * 录制开始
		 */
		void onRecordingStarted();
		
		/**
		 * 录制进度更新
		 * 
		 * @param currentDuration 当前录制时长（毫秒）
		 * @param remainingTime 剩余时间（毫秒）
		 */
		void onRecordingProgress(long currentDuration, long remainingTime);
		
		/**
		 * 录制完成
		 * 
		 * @param audioFile 录制的音频文件
		 * @param duration 录制时长（毫秒）
		 */
		void onRecordingCompleted(File audioFile, long duration);
		
		/**
		 * 录制取消
		 */
		void onRecordingCancelled();
		
		/**
		 * 录制错误
		 * 
		 * @param error 错误信息
		 */
		void onRecordingError(String error);
		
		/**
		 * 达到最大录制时长
		 */
		void onMaxDurationReached();
	}
}
