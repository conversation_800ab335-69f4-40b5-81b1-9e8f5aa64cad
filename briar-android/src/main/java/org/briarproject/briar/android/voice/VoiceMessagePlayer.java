package org.briarproject.briar.android.voice;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.os.Handler;
import android.os.Looper;

import org.briarproject.nullsafety.NotNullByDefault;

import java.io.File;
import java.io.IOException;
import java.util.logging.Logger;

import androidx.annotation.Nullable;
import androidx.annotation.UiThread;

import static java.util.logging.Level.WARNING;

/**
 * 语音消息播放器
 * 
 * 负责播放语音消息，包括播放控制、进度跟踪和状态管理。
 */
@UiThread
@NotNullByDefault
public class VoiceMessagePlayer {

	private static final Logger LOG = Logger.getLogger(VoiceMessagePlayer.class.getName());
	
	private final Context context;
	private final VoicePlayerListener listener;
	private final Handler handler = new Handler(Looper.getMainLooper());
	
	@Nullable
	private MediaPlayer mediaPlayer;
	@Nullable
	private File currentFile;
	private boolean isPlaying = false;
	private boolean isPrepared = false;
	
	// 播放进度更新间隔（毫秒）
	private static final int PROGRESS_UPDATE_INTERVAL = 100;
	
	public VoiceMessagePlayer(Context context, VoicePlayerListener listener) {
		this.context = context;
		this.listener = listener;
	}
	
	/**
	 * 准备播放指定的音频文件
	 * 
	 * @param audioFile 要播放的音频文件
	 * @return 是否成功准备
	 */
	public boolean prepareAudio(File audioFile) {
		if (audioFile == null || !audioFile.exists()) {
			listener.onPlaybackError("音频文件不存在");
			return false;
		}
		
		// 如果已经准备了相同的文件，直接返回
		if (isPrepared && audioFile.equals(currentFile)) {
			return true;
		}
		
		// 清理之前的播放器
		cleanup();
		
		try {
			currentFile = audioFile;
			mediaPlayer = new MediaPlayer();
			
			// 配置音频属性
			AudioAttributes audioAttributes = new AudioAttributes.Builder()
				.setUsage(AudioAttributes.USAGE_MEDIA)
				.setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
				.build();
			mediaPlayer.setAudioAttributes(audioAttributes);
			
			// 设置数据源
			mediaPlayer.setDataSource(audioFile.getAbsolutePath());
			
			// 设置监听器
			mediaPlayer.setOnPreparedListener(mp -> {
				isPrepared = true;
				listener.onAudioPrepared(mp.getDuration());
			});
			
			mediaPlayer.setOnCompletionListener(mp -> {
				isPlaying = false;
				handler.removeCallbacksAndMessages(null);
				listener.onPlaybackCompleted();
			});
			
			mediaPlayer.setOnErrorListener((mp, what, extra) -> {
				LOG.warning("MediaPlayer error: what=" + what + ", extra=" + extra);
				cleanup();
				listener.onPlaybackError("播放错误: " + what);
				return true;
			});
			
			// 异步准备
			mediaPlayer.prepareAsync();
			
			LOG.info("Preparing audio file: " + audioFile.getName());
			return true;
			
		} catch (IOException e) {
			LOG.log(WARNING, "Failed to prepare audio", e);
			cleanup();
			listener.onPlaybackError("音频准备失败: " + e.getMessage());
			return false;
		}
	}
	
	/**
	 * 开始播放
	 * 
	 * @return 是否成功开始播放
	 */
	public boolean startPlayback() {
		if (mediaPlayer == null || !isPrepared) {
			listener.onPlaybackError("音频未准备好");
			return false;
		}
		
		if (isPlaying) {
			return true; // 已经在播放
		}
		
		try {
			mediaPlayer.start();
			isPlaying = true;
			startProgressUpdates();
			listener.onPlaybackStarted();
			LOG.info("Voice playback started");
			return true;
			
		} catch (IllegalStateException e) {
			LOG.log(WARNING, "Failed to start playback", e);
			listener.onPlaybackError("播放启动失败");
			return false;
		}
	}
	
	/**
	 * 暂停播放
	 */
	public void pausePlayback() {
		if (mediaPlayer == null || !isPlaying) {
			return;
		}
		
		try {
			mediaPlayer.pause();
			isPlaying = false;
			handler.removeCallbacksAndMessages(null);
			listener.onPlaybackPaused();
			LOG.info("Voice playback paused");
			
		} catch (IllegalStateException e) {
			LOG.log(WARNING, "Failed to pause playback", e);
		}
	}
	
	/**
	 * 停止播放
	 */
	public void stopPlayback() {
		if (mediaPlayer == null) {
			return;
		}
		
		try {
			if (isPlaying) {
				mediaPlayer.stop();
				isPlaying = false;
				handler.removeCallbacksAndMessages(null);
			}
			
			// 重新准备以便下次播放
			if (currentFile != null) {
				mediaPlayer.reset();
				mediaPlayer.setDataSource(currentFile.getAbsolutePath());
				mediaPlayer.prepareAsync();
			}
			
			listener.onPlaybackStopped();
			LOG.info("Voice playback stopped");
			
		} catch (Exception e) {
			LOG.log(WARNING, "Failed to stop playback", e);
			cleanup();
		}
	}
	
	/**
	 * 跳转到指定位置
	 * 
	 * @param position 位置（毫秒）
	 */
	public void seekTo(int position) {
		if (mediaPlayer == null || !isPrepared) {
			return;
		}
		
		try {
			mediaPlayer.seekTo(position);
			listener.onSeekCompleted(position);
			
		} catch (IllegalStateException e) {
			LOG.log(WARNING, "Failed to seek", e);
		}
	}
	
	/**
	 * 获取当前播放位置（毫秒）
	 */
	public int getCurrentPosition() {
		if (mediaPlayer == null || !isPrepared) {
			return 0;
		}
		
		try {
			return mediaPlayer.getCurrentPosition();
		} catch (IllegalStateException e) {
			return 0;
		}
	}
	
	/**
	 * 获取音频总时长（毫秒）
	 */
	public int getDuration() {
		if (mediaPlayer == null || !isPrepared) {
			return 0;
		}
		
		try {
			return mediaPlayer.getDuration();
		} catch (IllegalStateException e) {
			return 0;
		}
	}
	
	/**
	 * 是否正在播放
	 */
	public boolean isPlaying() {
		return isPlaying;
	}
	
	/**
	 * 是否已准备好
	 */
	public boolean isPrepared() {
		return isPrepared;
	}
	
	/**
	 * 开始进度更新
	 */
	private void startProgressUpdates() {
		handler.post(new Runnable() {
			@Override
			public void run() {
				if (isPlaying && mediaPlayer != null) {
					int currentPosition = getCurrentPosition();
					int duration = getDuration();
					listener.onPlaybackProgress(currentPosition, duration);
					
					// 继续更新
					handler.postDelayed(this, PROGRESS_UPDATE_INTERVAL);
				}
			}
		});
	}
	
	/**
	 * 清理资源
	 */
	private void cleanup() {
		isPlaying = false;
		isPrepared = false;
		
		if (mediaPlayer != null) {
			try {
				mediaPlayer.release();
			} catch (RuntimeException e) {
				LOG.log(WARNING, "Error releasing MediaPlayer", e);
			}
			mediaPlayer = null;
		}
		
		currentFile = null;
		
		// 停止进度更新
		handler.removeCallbacksAndMessages(null);
	}
	
	/**
	 * 释放播放器资源
	 */
	public void release() {
		cleanup();
	}
	
	/**
	 * 语音播放监听器接口
	 */
	public interface VoicePlayerListener {
		
		/**
		 * 音频准备完成
		 * 
		 * @param duration 音频总时长（毫秒）
		 */
		void onAudioPrepared(int duration);
		
		/**
		 * 播放开始
		 */
		void onPlaybackStarted();
		
		/**
		 * 播放暂停
		 */
		void onPlaybackPaused();
		
		/**
		 * 播放停止
		 */
		void onPlaybackStopped();
		
		/**
		 * 播放完成
		 */
		void onPlaybackCompleted();
		
		/**
		 * 播放进度更新
		 * 
		 * @param currentPosition 当前位置（毫秒）
		 * @param duration 总时长（毫秒）
		 */
		void onPlaybackProgress(int currentPosition, int duration);
		
		/**
		 * 跳转完成
		 * 
		 * @param position 跳转到的位置（毫秒）
		 */
		void onSeekCompleted(int position);
		
		/**
		 * 播放错误
		 * 
		 * @param error 错误信息
		 */
		void onPlaybackError(String error);
	}
}
