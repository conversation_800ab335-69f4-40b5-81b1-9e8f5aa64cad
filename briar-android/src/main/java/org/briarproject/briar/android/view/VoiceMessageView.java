package org.briarproject.briar.android.view;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.SeekBar;
import android.widget.TextView;

import org.briarproject.briar.R;
import org.briarproject.briar.android.voice.VoiceMessagePlayer;
import org.briarproject.nullsafety.NotNullByDefault;

import java.io.File;
import java.util.Locale;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import static android.content.Context.LAYOUT_INFLATER_SERVICE;
import static java.util.Objects.requireNonNull;

/**
 * 语音消息显示控件
 * 
 * 用于显示和播放语音消息，包括播放控制、进度显示和时长信息。
 */
@NotNullByDefault
public class VoiceMessageView extends LinearLayout implements VoiceMessagePlayer.VoicePlayerListener {

	private final ImageButton playButton;
	private final SeekBar progressSeekBar;
	private final TextView durationText;
	private final ProgressBar loadingProgress;
	
	@Nullable
	private VoiceMessagePlayer player;
	@Nullable
	private File audioFile;
	@Nullable
	private VoiceMessageListener listener;
	
	private int totalDuration = 0;
	private boolean isUserSeeking = false;
	
	public VoiceMessageView(@NonNull Context context, @Nullable AttributeSet attrs) {
		super(context, attrs);
		setOrientation(HORIZONTAL);
		
		LayoutInflater inflater = (LayoutInflater) requireNonNull(
			context.getSystemService(LAYOUT_INFLATER_SERVICE));
		inflater.inflate(R.layout.view_voice_message, this, true);
		
		playButton = findViewById(R.id.playButton);
		progressSeekBar = findViewById(R.id.progressSeekBar);
		durationText = findViewById(R.id.durationText);
		loadingProgress = findViewById(R.id.loadingProgress);
		
		setupViews();
	}
	
	private void setupViews() {
		// 播放按钮点击事件
		playButton.setOnClickListener(v -> {
			if (player != null && player.isPrepared()) {
				if (player.isPlaying()) {
					player.pausePlayback();
				} else {
					player.startPlayback();
				}
			} else if (audioFile != null && player != null) {
				player.prepareAudio(audioFile);
			}
		});
		
		// 进度条拖拽事件
		progressSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
			@Override
			public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
				if (fromUser && player != null && player.isPrepared()) {
					int position = (int) ((progress / 100.0) * totalDuration);
					updateDurationText(position, totalDuration);
				}
			}
			
			@Override
			public void onStartTrackingTouch(SeekBar seekBar) {
				isUserSeeking = true;
			}
			
			@Override
			public void onStopTrackingTouch(SeekBar seekBar) {
				isUserSeeking = false;
				if (player != null && player.isPrepared()) {
					int position = (int) ((seekBar.getProgress() / 100.0) * totalDuration);
					player.seekTo(position);
				}
			}
		});
		
		// 初始状态
		setPlayButtonState(false, false);
		progressSeekBar.setProgress(0);
		durationText.setText("0:00");
		showLoading(false);
	}
	
	/**
	 * 设置语音消息文件
	 * 
	 * @param audioFile 音频文件
	 * @param player 播放器实例
	 */
	public void setVoiceMessage(File audioFile, VoiceMessagePlayer player) {
		this.audioFile = audioFile;
		this.player = player;
		
		if (audioFile != null && audioFile.exists()) {
			showLoading(true);
			setPlayButtonState(false, false);
			player.prepareAudio(audioFile);
		}
	}
	
	/**
	 * 设置监听器
	 */
	public void setVoiceMessageListener(@Nullable VoiceMessageListener listener) {
		this.listener = listener;
	}
	
	/**
	 * 设置播放按钮状态
	 * 
	 * @param isPlaying 是否正在播放
	 * @param enabled 是否启用
	 */
	private void setPlayButtonState(boolean isPlaying, boolean enabled) {
		playButton.setEnabled(enabled);
		if (isPlaying) {
			playButton.setImageResource(R.drawable.ic_pause_24dp);
		} else {
			playButton.setImageResource(R.drawable.ic_play_arrow_24dp);
		}
	}
	
	/**
	 * 显示/隐藏加载进度
	 */
	private void showLoading(boolean show) {
		loadingProgress.setVisibility(show ? VISIBLE : GONE);
		playButton.setVisibility(show ? GONE : VISIBLE);
	}
	
	/**
	 * 更新时长显示
	 */
	private void updateDurationText(int currentPosition, int duration) {
		String current = formatDuration(currentPosition);
		String total = formatDuration(duration);
		durationText.setText(String.format(Locale.getDefault(), "%s / %s", current, total));
	}
	
	/**
	 * 格式化时长显示
	 */
	private String formatDuration(int milliseconds) {
		int seconds = milliseconds / 1000;
		int minutes = seconds / 60;
		seconds = seconds % 60;
		return String.format(Locale.getDefault(), "%d:%02d", minutes, seconds);
	}
	
	// VoicePlayerListener 实现
	
	@Override
	public void onAudioPrepared(int duration) {
		totalDuration = duration;
		showLoading(false);
		setPlayButtonState(false, true);
		updateDurationText(0, duration);
		progressSeekBar.setProgress(0);
		
		if (listener != null) {
			listener.onVoiceMessagePrepared(duration);
		}
	}
	
	@Override
	public void onPlaybackStarted() {
		setPlayButtonState(true, true);
		
		if (listener != null) {
			listener.onVoiceMessagePlaybackStarted();
		}
	}
	
	@Override
	public void onPlaybackPaused() {
		setPlayButtonState(false, true);
		
		if (listener != null) {
			listener.onVoiceMessagePlaybackPaused();
		}
	}
	
	@Override
	public void onPlaybackStopped() {
		setPlayButtonState(false, true);
		progressSeekBar.setProgress(0);
		updateDurationText(0, totalDuration);
		
		if (listener != null) {
			listener.onVoiceMessagePlaybackStopped();
		}
	}
	
	@Override
	public void onPlaybackCompleted() {
		setPlayButtonState(false, true);
		progressSeekBar.setProgress(0);
		updateDurationText(0, totalDuration);
		
		if (listener != null) {
			listener.onVoiceMessagePlaybackCompleted();
		}
	}
	
	@Override
	public void onPlaybackProgress(int currentPosition, int duration) {
		if (!isUserSeeking) {
			int progress = duration > 0 ? (int) ((currentPosition * 100.0) / duration) : 0;
			progressSeekBar.setProgress(progress);
			updateDurationText(currentPosition, duration);
		}
	}
	
	@Override
	public void onSeekCompleted(int position) {
		updateDurationText(position, totalDuration);
	}
	
	@Override
	public void onPlaybackError(String error) {
		showLoading(false);
		setPlayButtonState(false, false);
		
		if (listener != null) {
			listener.onVoiceMessageError(error);
		}
	}
	
	/**
	 * 释放资源
	 */
	public void release() {
		if (player != null) {
			player.release();
		}
	}
	
	/**
	 * 语音消息监听器接口
	 */
	public interface VoiceMessageListener {
		
		/**
		 * 语音消息准备完成
		 * 
		 * @param duration 音频时长（毫秒）
		 */
		void onVoiceMessagePrepared(int duration);
		
		/**
		 * 播放开始
		 */
		void onVoiceMessagePlaybackStarted();
		
		/**
		 * 播放暂停
		 */
		void onVoiceMessagePlaybackPaused();
		
		/**
		 * 播放停止
		 */
		void onVoiceMessagePlaybackStopped();
		
		/**
		 * 播放完成
		 */
		void onVoiceMessagePlaybackCompleted();
		
		/**
		 * 播放错误
		 * 
		 * @param error 错误信息
		 */
		void onVoiceMessageError(String error);
	}
}
