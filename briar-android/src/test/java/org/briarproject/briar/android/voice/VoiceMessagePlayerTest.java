package org.briarproject.briar.android.voice;

import android.content.Context;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.robolectric.RobolectricTestRunner;
import org.robolectric.RuntimeEnvironment;
import org.robolectric.annotation.Config;

import java.io.File;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.verify;

/**
 * 语音消息播放器测试
 */
@RunWith(RobolectricTestRunner.class)
@Config(sdk = 28)
public class VoiceMessagePlayerTest {

	@Mock
	private VoiceMessagePlayer.VoicePlayerListener mockListener;

	private Context context;
	private VoiceMessagePlayer player;

	@Before
	public void setUp() {
		MockitoAnnotations.openMocks(this);
		context = RuntimeEnvironment.getApplication();
		player = new VoiceMessagePlayer(context, mockListener);
	}

	@Test
	public void testPrepareAudio_NullFile() {
		boolean result = player.prepareAudio(null);
		assertFalse(result);
		verify(mockListener).onPlaybackError("音频文件不存在");
	}

	@Test
	public void testPrepareAudio_NonExistentFile() {
		File nonExistentFile = new File("/non/existent/file.aac");
		boolean result = player.prepareAudio(nonExistentFile);
		assertFalse(result);
		verify(mockListener).onPlaybackError("音频文件不存在");
	}

	@Test
	public void testStartPlayback_NotPrepared() {
		boolean result = player.startPlayback();
		assertFalse(result);
		verify(mockListener).onPlaybackError("音频未准备好");
	}

	@Test
	public void testIsPlaying_InitialState() {
		assertFalse(player.isPlaying());
	}

	@Test
	public void testIsPrepared_InitialState() {
		assertFalse(player.isPrepared());
	}

	@Test
	public void testGetCurrentPosition_NotPrepared() {
		int position = player.getCurrentPosition();
		assertEquals(0, position);
	}

	@Test
	public void testGetDuration_NotPrepared() {
		int duration = player.getDuration();
		assertEquals(0, duration);
	}

	@Test
	public void testPausePlayback_NotPlaying() {
		// 测试暂停播放不会抛出异常
		player.pausePlayback();
		// 没有异常就是成功
		assertTrue(true);
	}

	@Test
	public void testStopPlayback_NotPlaying() {
		// 测试停止播放不会抛出异常
		player.stopPlayback();
		// 没有异常就是成功
		assertTrue(true);
	}

	@Test
	public void testSeekTo_NotPrepared() {
		// 测试跳转不会抛出异常
		player.seekTo(1000);
		// 没有异常就是成功
		assertTrue(true);
	}

	@Test
	public void testRelease() {
		// 测试释放资源不会抛出异常
		player.release();
		assertFalse(player.isPlaying());
		assertFalse(player.isPrepared());
	}
}
