<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.robolectric</groupId>
  <artifactId>android-all-instrumented</artifactId>
  <version>9-robolectric-4913185-2-i4</version>
  <name>Google Android 9 instrumented android-all library</name>
  <description>Google Android 9 framework jars transformed with Robolectric instrumentation.</description>
  <url>https://source.android.com/</url>
  <inceptionYear>2008</inceptionYear>
  <licenses>
    <license>
      <name>Apache 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0</url>
      <distribution>repo</distribution>
      <comments>While the EULA for the Android SDK restricts distribution of those binaries, the source code is licensed under Apache 2.0 which allows compiling binaries from source and then distributing those versions.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>The Android Open Source Projects</name>
    </developer>
  </developers>
  <scm>
    <connection>https://android.googlesource.com/platform/manifest.git</connection>
    <url>https://android.googlesource.com/platform/manifest.git</url>
  </scm>
</project>
