# Briar数据库层架构详解

## 🗄️ 数据库层概述

Briar采用了分层的数据库架构，支持多种数据库后端（H2、HyperSQL），提供了完整的事务管理、数据迁移、加密存储等企业级特性。

### 核心设计原则
- **数据库无关性**：通过抽象层支持多种数据库
- **事务安全**：完整的ACID事务支持
- **数据加密**：所有数据都经过加密存储
- **版本管理**：自动的数据库模式迁移

## 🏗️ 数据库架构

```mermaid
graph TB
    subgraph "应用层"
        APP[应用程序]
        SERVICE[业务服务]
    end
    
    subgraph "数据库组件层"
        DC[DatabaseComponent]
        TM[TransactionManager]
        EM[EventManager]
    end
    
    subgraph "数据库抽象层"
        DB[Database Interface]
        JDBC[JdbcDatabase]
    end
    
    subgraph "数据库实现层"
        H2[H2Database]
        HSQL[HyperSqlDatabase]
    end
    
    subgraph "数据库引擎"
        H2DB[(H2 Database)]
        HSQLDB[(HyperSQL Database)]
    end
    
    subgraph "存储层"
        FILE[加密文件存储]
        BACKUP[备份存储]
    end
    
    APP --> SERVICE
    SERVICE --> DC
    DC --> TM
    DC --> EM
    TM --> DB
    DB --> JDBC
    JDBC --> H2
    JDBC --> HSQL
    H2 --> H2DB
    HSQL --> HSQLDB
    H2DB --> FILE
    HSQLDB --> FILE
    FILE --> BACKUP
```

## 📊 数据库模式设计

### 核心表结构

#### 1. 设置表 (settings)
```sql
CREATE TABLE settings (
    namespace VARCHAR NOT NULL,
    settingKey VARCHAR NOT NULL,
    value VARCHAR NOT NULL,
    PRIMARY KEY (namespace, settingKey)
);
```

#### 2. 本地作者表 (localAuthors)
```sql
CREATE TABLE localAuthors (
    authorId BINARY(32) NOT NULL,
    formatVersion INT NOT NULL,
    name VARCHAR NOT NULL,
    publicKey BINARY NOT NULL,
    privateKey BINARY NOT NULL,
    created BIGINT NOT NULL,
    PRIMARY KEY (authorId)
);
```

#### 3. 联系人表 (contacts)
```sql
CREATE TABLE contacts (
    contactId INT NOT NULL AUTO_INCREMENT,
    authorId BINARY(32) NOT NULL,
    formatVersion INT NOT NULL,
    name VARCHAR NOT NULL,
    alias VARCHAR,
    publicKey BINARY NOT NULL,
    handshakePublicKey BINARY,
    localAuthorId BINARY(32) NOT NULL,
    verified BOOLEAN NOT NULL,
    syncVersions BINARY DEFAULT '00' NOT NULL,
    PRIMARY KEY (contactId),
    FOREIGN KEY (localAuthorId) REFERENCES localAuthors (authorId)
);
```

#### 4. 群组表 (groups)
```sql
CREATE TABLE groups (
    groupId BINARY(32) NOT NULL,
    clientId VARCHAR NOT NULL,
    majorVersion INT NOT NULL,
    descriptor BLOB NOT NULL,
    PRIMARY KEY (groupId)
);
```

#### 5. 消息表 (messages)
```sql
CREATE TABLE messages (
    messageId BINARY(32) NOT NULL,
    groupId BINARY(32) NOT NULL,
    timestamp BIGINT NOT NULL,
    state INT NOT NULL,
    shared BOOLEAN NOT NULL,
    temporary BOOLEAN NOT NULL,
    cleanupTimerDuration BIGINT,
    cleanupDeadline BIGINT,
    length INT NOT NULL,
    raw BLOB,
    PRIMARY KEY (messageId),
    FOREIGN KEY (groupId) REFERENCES groups (groupId)
);
```

#### 6. 消息状态表 (statuses)
```sql
CREATE TABLE statuses (
    messageId BINARY(32) NOT NULL,
    contactId INT NOT NULL,
    groupId BINARY(32) NOT NULL,
    timestamp BIGINT NOT NULL,
    length INT NOT NULL,
    state INT NOT NULL,
    groupShared BOOLEAN NOT NULL,
    messageShared BOOLEAN NOT NULL,
    deleted BOOLEAN NOT NULL,
    ack BOOLEAN NOT NULL,
    seen BOOLEAN NOT NULL,
    requested BOOLEAN NOT NULL,
    expiry BIGINT NOT NULL,
    txCount INT NOT NULL,
    maxLatency BIGINT,
    PRIMARY KEY (messageId, contactId),
    FOREIGN KEY (messageId) REFERENCES messages (messageId),
    FOREIGN KEY (contactId) REFERENCES contacts (contactId)
);
```

## 🔧 数据库实现

### H2数据库实现

#### 连接配置
```java
public class H2Database extends JdbcDatabase {
    private static final String HASH_TYPE = "BINARY(32)";
    private static final String SECRET_TYPE = "BINARY(32)";
    private static final String BINARY_TYPE = "BINARY";
    private static final String COUNTER_TYPE = "INT NOT NULL AUTO_INCREMENT";
    private static final String STRING_TYPE = "VARCHAR";
    
    private final String url;
    
    H2Database(DatabaseConfig config, MessageFactory messageFactory, Clock clock) {
        super(dbTypes, messageFactory, clock);
        File dir = config.getDatabaseDirectory();
        String path = new File(dir, "db").getAbsolutePath();
        // 配置加密和多线程支持
        url = "jdbc:h2:split:" + path + ";CIPHER=AES;MULTI_THREADED=1;WRITE_DELAY=0";
    }
}
```

#### 加密连接
```java
@Override
protected Connection createConnection() throws DbException, SQLException {
    SecretKey key = this.key;
    if (key == null) throw new DbClosedException();
    
    Properties props = new Properties();
    props.setProperty("user", "user");
    // 将文件密码和用户密码用空格分隔
    String hex = StringUtils.toHexString(key.getBytes());
    props.put("password", hex + " password");
    
    return DriverManager.getConnection(getUrl(), props);
}
```

### HyperSQL数据库实现

#### 连接配置
```java
public class HyperSqlDatabase extends JdbcDatabase {
    HyperSqlDatabase(DatabaseConfig config, MessageFactory messageFactory, Clock clock) {
        super(dbTypes, messageFactory, clock);
        File dir = config.getDatabaseDirectory();
        String path = new File(dir, "db").getAbsolutePath();
        url = "jdbc:hsqldb:file:" + path
                + ";sql.enforce_size=false;allow_empty_batch=true"
                + ";encrypt_lobs=true;crypt_type=AES";
    }
}
```

#### 优雅关闭
```java
@Override
public void close() throws DbException {
    Connection c = null;
    Statement s = null;
    try {
        closeAllConnections();
        c = createConnection();
        setDirty(c, false);
        s = c.createStatement();
        s.executeQuery("SHUTDOWN COMPACT");  // 压缩数据库
        s.close();
        c.close();
    } catch (SQLException e) {
        tryToClose(s, LOG, WARNING);
        tryToClose(c, LOG, WARNING);
        throw new DbException(e);
    }
}
```

## 🔄 事务管理

### 事务管理器实现

#### 读写锁机制
```java
public class DatabaseComponentImpl implements DatabaseComponent, TransactionManager {
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    
    @Override
    public Transaction startTransaction(boolean readOnly) throws DbException {
        // 防止重入锁定
        if (lock.getReadHoldCount() > 0) throw new IllegalStateException();
        if (lock.getWriteHoldCount() > 0) throw new IllegalStateException();
        
        long start = now();
        if (readOnly) {
            lock.readLock().lock();
            logDuration(LOG, "Waiting for read lock", start);
        } else {
            lock.writeLock().lock();
            logDuration(LOG, "Waiting for write lock", start);
        }
        
        try {
            return new Transaction(db.startTransaction(), readOnly);
        } catch (DbException | RuntimeException e) {
            if (readOnly) lock.readLock().unlock();
            else lock.writeLock().unlock();
            throw e;
        }
    }
}
```

#### 事务模板方法
```java
@Override
public <E extends Exception> void transaction(boolean readOnly, DbRunnable<E> task) 
        throws DbException, E {
    Transaction txn = startTransaction(readOnly);
    try {
        task.run(txn);
        commitTransaction(txn);
    } finally {
        endTransaction(txn);
    }
}

@Override
public <R, E extends Exception> R transactionWithResult(boolean readOnly, 
        DbCallable<R, E> task) throws DbException, E {
    Transaction txn = startTransaction(readOnly);
    try {
        R result = task.call(txn);
        commitTransaction(txn);
        return result;
    } finally {
        endTransaction(txn);
    }
}
```

### 连接池管理

#### 连接池实现
```java
public abstract class JdbcDatabase implements Database<Connection> {
    private final Queue<Connection> connectionPool = new LinkedList<>();
    private int openConnections = 0;
    private final Lock connectionsLock = new ReentrantLock();
    private final Condition connectionsChanged = connectionsLock.newCondition();
    
    @Override
    public Connection startTransaction() throws DbException {
        Connection txn;
        connectionsLock.lock();
        try {
            if (closed) throw new DbClosedException();
            txn = connectionPool.poll();  // 从池中获取连接
            logConnectionCounts();
        } finally {
            connectionsLock.unlock();
        }
        
        try {
            if (txn == null) {
                // 创建新连接
                txn = createConnection();
                txn.setAutoCommit(false);
                connectionsLock.lock();
                try {
                    if (closed) {
                        tryToClose(txn, LOG, WARNING);
                        throw new DbClosedException();
                    }
                    openConnections++;
                    logConnectionCounts();
                    connectionsChanged.signalAll();
                } finally {
                    connectionsLock.unlock();
                }
            }
        } catch (SQLException e) {
            throw new DbException(e);
        }
        return txn;
    }
}
```

## 🔄 数据库迁移

### 迁移机制

#### 版本检查和迁移
```java
private boolean migrateSchema(Connection txn, Settings s, 
        @Nullable MigrationListener listener) throws DbException {
    int dataSchemaVersion = s.getInt(SCHEMA_VERSION_KEY, -1);
    if (dataSchemaVersion == -1) throw new DbException();
    if (dataSchemaVersion == CODE_SCHEMA_VERSION) return false;
    if (CODE_SCHEMA_VERSION < dataSchemaVersion)
        throw new DataTooNewException();
    
    // 按顺序应用适当的迁移
    for (Migration<Connection> m : getMigrations()) {
        int start = m.getStartVersion(), end = m.getEndVersion();
        if (start == dataSchemaVersion) {
            if (LOG.isLoggable(INFO))
                LOG.info("Migrating from schema " + start + " to " + end);
            if (listener != null) listener.onDatabaseMigration();
            
            // 应用迁移
            m.migrate(txn);
            // 存储新的模式版本
            storeSchemaVersion(txn, end);
            dataSchemaVersion = end;
        }
    }
    
    if (dataSchemaVersion != CODE_SCHEMA_VERSION)
        throw new DataTooOldException();
    return true;
}
```

#### 迁移列表
```java
List<Migration<Connection>> getMigrations() {
    return asList(
        new Migration38_39(),
        new Migration39_40(),
        new Migration40_41(dbTypes),
        new Migration41_42(dbTypes),
        new Migration42_43(dbTypes),
        new Migration43_44(dbTypes),
        new Migration44_45(),
        new Migration45_46(),
        new Migration46_47(dbTypes),
        new Migration47_48(),
        new Migration48_49(),
        new Migration49_50()
    );
}
```

## 🔒 数据安全

### 加密存储
- **文件级加密**：整个数据库文件使用AES加密
- **密钥管理**：使用用户密码派生的密钥
- **传输加密**：连接字符串中包含加密参数

### 数据完整性
- **事务ACID**：保证数据的原子性、一致性、隔离性、持久性
- **外键约束**：维护数据引用完整性
- **脏标志检测**：检测异常关闭导致的数据不一致

### 备份和恢复
- **自动备份**：定期创建数据库快照
- **压缩存储**：使用SHUTDOWN COMPACT优化存储
- **故障恢复**：检测和处理数据库损坏

这种分层的数据库架构为Briar提供了可靠、安全、高性能的数据存储基础，支持复杂的分布式通信需求。
