# Briar数据库架构深度解析

## 概述

Briar项目采用了分层的数据库架构设计，通过抽象层将业务逻辑与具体的数据库实现分离。这种设计使得Briar能够支持多种数据库后端，同时保持代码的可维护性和可测试性。

## 架构层次

### 1. API层 (bramble-api)

API层定义了数据库操作的接口规范，主要包括：

```
bramble-api/
├── org.briarproject.bramble.api.db/
│   ├── Database.java              # 数据库接口
│   ├── DatabaseComponent.java     # 数据库组件接口
│   ├── Transaction.java           # 事务接口
│   ├── TransactionManager.java    # 事务管理器接口
│   └── Metadata.java             # 元数据接口
```

**核心接口说明：**

- `Database`: 定义了数据库的基本操作方法
- `DatabaseComponent`: 提供高级数据库操作，如联系人、消息管理
- `Transaction`: 表示数据库事务的抽象
- `TransactionManager`: 管理事务的生命周期

### 2. 核心实现层 (bramble-core)

核心层提供了数据库接口的具体实现：

```
bramble-core/
├── org.briarproject.bramble.db/
│   ├── DatabaseComponentImpl.java    # 数据库组件实现
│   ├── TransactionManagerImpl.java   # 事务管理器实现
│   ├── H2Database.java              # H2数据库实现
│   ├── HyperSqlDatabase.java        # HyperSQL数据库实现
│   └── Migration.java               # 数据库迁移
```

### 3. 平台适配层

不同平台有特定的数据库适配：

- **Android平台** (bramble-android): 使用SQLite数据库
- **Java平台** (bramble-java): 支持H2和HyperSQL数据库

## 数据模型

### 核心实体

#### 1. Author (作者)
```java
public class Author {
    private final AuthorId id;          // 作者唯一标识
    private final String name;          // 作者名称
    private final PublicKey publicKey;   // 公钥
}
```

#### 2. Contact (联系人)
```java
public class Contact {
    private final ContactId contactId;   // 联系人ID
    private final Author author;         // 联系人作者信息
    private final AuthorId localAuthorId; // 本地作者ID
    private final boolean verified;      // 是否已验证
    private final boolean active;        // 是否活跃
}
```

#### 3. Group (群组)
```java
public class Group {
    private final GroupId id;           // 群组ID
    private final ClientId clientId;    // 客户端ID
    private final int majorVersion;     // 主版本号
    private final byte[] descriptor;    // 群组描述符
}
```

#### 4. Message (消息)
```java
public class Message {
    private final MessageId id;         // 消息ID
    private final GroupId groupId;      // 所属群组ID
    private final long timestamp;       // 时间戳
    private final byte[] body;          // 消息体
}
```

### 数据库表结构

#### 主要表

1. **authors** - 存储作者信息
   - `authorId` (PRIMARY KEY)
   - `name`
   - `publicKey`

2. **contacts** - 存储联系人关系
   - `contactId` (PRIMARY KEY)
   - `authorId` (FOREIGN KEY)
   - `localAuthorId` (FOREIGN KEY)
   - `verified`
   - `active`

3. **groups** - 存储群组信息
   - `groupId` (PRIMARY KEY)
   - `clientId`
   - `majorVersion`
   - `descriptor`

4. **messages** - 存储消息
   - `messageId` (PRIMARY KEY)
   - `groupId` (FOREIGN KEY)
   - `timestamp`
   - `body`

5. **settings** - 存储配置信息
   - `namespace`
   - `key`
   - `value`

## 事务管理

### 事务类型

Briar支持两种类型的事务：

1. **只读事务** (`readOnly = true`)
   - 用于查询操作
   - 不会修改数据库状态
   - 支持更高的并发性

2. **读写事务** (`readOnly = false`)
   - 用于修改操作
   - 可以读取和修改数据
   - 需要独占访问

### 事务生命周期

```java
// 事务的典型生命周期
transactionManager.transaction(false, txn -> {
    // 1. 事务开始
    // 2. 执行数据库操作
    databaseComponent.addLocalAuthor(txn, author);
    // 3. 事务提交（自动）
    return null;
});
// 4. 事务结束
```

### 异常处理和回滚

```java
try {
    transactionManager.transaction(false, txn -> {
        // 数据库操作
        if (errorCondition) {
            throw new RuntimeException("业务错误");
        }
        return null;
    });
} catch (Exception e) {
    // 事务已自动回滚
    handleError(e);
}
```

## 数据库实现

### H2数据库

H2是一个纯Java实现的关系数据库，主要特点：

- **轻量级**: 小于2MB的JAR文件
- **快速**: 优秀的性能表现
- **嵌入式**: 可以嵌入到应用程序中
- **标准SQL**: 支持标准SQL语法

**配置示例：**
```java
String url = "jdbc:h2:file:" + databasePath + ";CIPHER=AES";
Connection connection = DriverManager.getConnection(url, "sa", password);
```

### HyperSQL数据库

HyperSQL (HSQLDB) 是另一个纯Java数据库引擎：

- **成熟稳定**: 长期开发和维护
- **标准兼容**: 高度兼容SQL标准
- **灵活配置**: 支持多种运行模式
- **事务支持**: 完整的ACID事务支持

**配置示例：**
```java
String url = "jdbc:hsqldb:file:" + databasePath + ";crypt_key=" + password;
Connection connection = DriverManager.getConnection(url, "SA", "");
```

### SQLite (Android)

在Android平台上，Briar使用SQLite数据库：

- **系统集成**: Android系统原生支持
- **轻量级**: 适合移动设备
- **可靠性**: 经过广泛测试和使用
- **API友好**: 提供便捷的Android API

## 数据库迁移

### 版本管理

Briar使用版本号来管理数据库schema的变更：

```java
public class Migration {
    private final int fromVersion;
    private final int toVersion;
    private final String sql;
    
    // 迁移逻辑
}
```

### 迁移策略

1. **向前兼容**: 新版本能够处理旧版本的数据
2. **增量更新**: 只应用必要的变更
3. **数据保护**: 迁移过程中保护用户数据
4. **回滚支持**: 在可能的情况下支持回滚

### 迁移示例

```java
// 从版本1迁移到版本2
Migration migration = new Migration(1, 2, 
    "ALTER TABLE contacts ADD COLUMN lastSeen BIGINT DEFAULT 0");
```

## 性能优化

### 索引策略

Briar在关键字段上创建索引以提高查询性能：

```sql
-- 消息查询优化
CREATE INDEX idx_messages_group_timestamp ON messages(groupId, timestamp);

-- 联系人查询优化  
CREATE INDEX idx_contacts_author ON contacts(authorId);
```

### 查询优化

1. **批量操作**: 使用批量插入和更新
2. **预编译语句**: 使用PreparedStatement
3. **连接池**: 合理管理数据库连接
4. **缓存策略**: 缓存频繁访问的数据

### 内存管理

```java
// 大结果集的处理
transactionManager.transactionWithResult(true, txn -> {
    // 使用流式处理避免内存溢出
    return databaseComponent.getMessagesStream(txn, groupId)
        .limit(1000)
        .collect(Collectors.toList());
});
```

## 安全考虑

### 数据加密

1. **数据库加密**: 整个数据库文件加密
2. **字段加密**: 敏感字段单独加密
3. **密钥管理**: 安全的密钥存储和管理

### 访问控制

1. **事务隔离**: 通过事务确保数据一致性
2. **权限检查**: 在业务层进行权限验证
3. **SQL注入防护**: 使用参数化查询

## 测试策略

### 单元测试

```java
@Test
public void testDatabaseOperations() {
    // 使用内存数据库进行测试
    Database testDb = new H2Database(":memory:");
    // 测试逻辑
}
```

### 集成测试

```java
@Test
public void testTransactionIntegration() {
    // 测试事务管理器与数据库的集成
    transactionManager.transaction(false, txn -> {
        // 验证事务行为
        return null;
    });
}
```

### 性能测试

```java
@Test
public void testPerformance() {
    long startTime = System.currentTimeMillis();
    // 执行大量数据库操作
    long duration = System.currentTimeMillis() - startTime;
    assertTrue("性能测试", duration < 1000);
}
```

## 最佳实践

1. **事务边界**: 保持事务简短和明确
2. **错误处理**: 适当的异常处理和日志记录
3. **资源管理**: 及时释放数据库资源
4. **监控**: 监控数据库性能和健康状态
5. **备份**: 定期备份重要数据

## 扩展性考虑

1. **水平扩展**: 支持数据分片和分布式部署
2. **垂直扩展**: 优化单机性能
3. **插件架构**: 支持自定义数据库后端
4. **API稳定性**: 保持API的向后兼容性

---

这个架构文档提供了Briar数据库系统的全面概述。通过理解这些概念和实现细节，开发者可以更好地使用和扩展Briar的数据库功能。
