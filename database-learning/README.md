# Briar数据库学习项目

这个项目专门用于学习和理解Briar项目中的数据库层实现。通过实际的代码示例和详细的文档，帮助开发者深入了解Briar的数据库架构。

## 项目结构

```
database-learning/
├── README.md                    # 本文档
├── src/main/java/
│   └── org/briarproject/learning/
│       ├── DatabaseLearningExample.java    # 主要学习示例
│       ├── TransactionExample.java         # 事务管理示例
│       ├── ContactExample.java             # 联系人管理示例
│       └── PerformanceExample.java         # 性能优化示例
├── docs/
│   ├── database-architecture.md            # 数据库架构文档
│   ├── transaction-management.md           # 事务管理指南
│   └── best-practices.md                   # 最佳实践
└── tests/
    └── DatabaseLearningTest.java           # 单元测试
```

## 学习目标

通过这个项目，你将学会：

1. **数据库架构理解**
   - Briar的数据库抽象层设计
   - DatabaseComponent的作用和使用方法
   - 数据库事务的生命周期管理

2. **事务管理**
   - 只读事务 vs 写事务
   - 事务的正确使用模式
   - 错误处理和回滚机制

3. **数据操作**
   - 作者身份的创建和管理
   - 联系人关系的建立和维护
   - 消息存储和检索

4. **性能优化**
   - 批量操作的最佳实践
   - 查询优化技巧
   - 数据库连接池管理

## 核心概念

### 1. DatabaseComponent

DatabaseComponent是Briar数据库操作的核心接口，提供了：

- 作者管理：`addLocalAuthor()`, `getLocalAuthors()`
- 联系人管理：`addContact()`, `getContacts()`
- 消息管理：`addMessage()`, `getMessages()`
- 设置管理：`mergeSettings()`, `getSettings()`

### 2. TransactionManager

TransactionManager负责数据库事务的管理：

```java
// 只读事务
transactionManager.transactionWithResult(true, txn -> {
    return databaseComponent.getLocalAuthors(txn);
});

// 写事务
transactionManager.transaction(false, txn -> {
    databaseComponent.addLocalAuthor(txn, author);
    return null;
});
```

### 3. 数据模型

主要的数据实体包括：

- **Author**: 用户身份，包含公钥和名称
- **Contact**: 联系人关系，连接本地作者和远程作者
- **Message**: 消息内容，包含发送者、接收者和内容
- **Group**: 群组信息，用于群聊功能

## 运行示例

### 前提条件

1. 确保已经构建了Briar项目：
   ```bash
   ./gradlew build
   ```

2. 设置正确的类路径，包含所有必要的Briar依赖

### 运行方法

1. **在IDE中运行**：
   - 导入项目到你的IDE
   - 配置类路径包含Briar的JAR文件
   - 运行`DatabaseLearningExample.main()`

2. **命令行运行**：
   ```bash
   # 编译
   javac -cp "path/to/briar/jars/*" src/main/java/org/briarproject/learning/*.java
   
   # 运行
   java -cp "path/to/briar/jars/*:src/main/java" org.briarproject.learning.DatabaseLearningExample
   ```

## 学习路径

### 第一阶段：基础理解
1. 阅读`DatabaseLearningExample.java`中的基础示例
2. 理解数据库组件的初始化过程
3. 学习简单的CRUD操作

### 第二阶段：事务管理
1. 深入学习事务的概念和重要性
2. 实践只读事务和写事务的区别
3. 理解事务回滚机制

### 第三阶段：实际应用
1. 创建完整的联系人管理功能
2. 实现消息存储和检索
3. 添加错误处理和日志记录

### 第四阶段：性能优化
1. 学习批量操作技巧
2. 优化查询性能
3. 监控数据库性能指标

## 常见问题

### Q: 如何初始化数据库组件？
A: 在实际的Briar应用中，数据库组件通过依赖注入框架（Dagger）自动初始化。在学习环境中，你需要手动创建和配置这些组件。

### Q: 事务什么时候会自动回滚？
A: 当事务执行过程中抛出任何未捕获的异常时，事务会自动回滚。这确保了数据的一致性。

### Q: 如何处理并发访问？
A: Briar使用数据库级别的锁机制来处理并发访问。TransactionManager确保事务的原子性和隔离性。

## 进阶学习

1. **源码阅读**：
   - 阅读`bramble-core`模块中的数据库实现
   - 理解H2和HyperSQL数据库的适配层

2. **测试编写**：
   - 为你的数据库操作编写单元测试
   - 学习使用内存数据库进行测试

3. **性能分析**：
   - 使用数据库性能分析工具
   - 优化查询和索引策略

## 参考资源

- [Briar官方文档](https://briarproject.org/)
- [Briar源码仓库](https://code.briarproject.org/briar/briar)
- [H2数据库文档](http://www.h2database.com/)
- [HyperSQL文档](http://hsqldb.org/)

## 贡献

如果你发现了问题或有改进建议，请：

1. 创建Issue描述问题
2. 提交Pull Request with fixes
3. 更新文档和示例

---

**注意**：这个学习项目是为了教育目的而创建的。在生产环境中使用Briar数据库功能时，请遵循官方文档和最佳实践。
