package database.learning;

import org.junit.Test;
import org.junit.Before;
import org.junit.After;
import java.sql.*;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.io.File;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import static org.junit.Assert.*;

/**
 * 数据库性能学习实践
 * 
 * 学习目标：
 * 1. 理解数据库性能优化技术
 * 2. 掌握索引设计和查询优化
 * 3. 学习批量操作和事务优化
 * 4. 实现数据库性能监控和分析
 */
public class DatabasePerformanceLearning {
    
    private File testDbDir;
    private DatabasePerformanceManager performanceManager;
    
    @Before
    public void setUp() throws Exception {
        testDbDir = new File(System.getProperty("java.io.tmpdir"), "briar-performance-test");
        if (testDbDir.exists()) {
            deleteDirectory(testDbDir);
        }
        testDbDir.mkdirs();
        
        performanceManager = new DatabasePerformanceManager(testDbDir);
    }
    
    @After
    public void tearDown() throws Exception {
        if (performanceManager != null) {
            performanceManager.close();
        }
        if (testDbDir != null && testDbDir.exists()) {
            deleteDirectory(testDbDir);
        }
    }
    
    /**
     * 测试1：基础CRUD操作性能
     */
    @Test
    public void testBasicCrudPerformance() throws Exception {
        System.out.println("⚡ 开始基础CRUD操作性能测试");
        System.out.println("=" .repeat(50));
        
        SecretKey key = generateEncryptionKey();
        performanceManager.initialize(key);
        
        int testSize = 10000;
        
        // 测试插入性能
        System.out.println("📝 测试插入性能:");
        
        PerformanceMetrics insertMetrics = performanceManager.measureInsertPerformance(testSize);
        
        System.out.println("  记录数: " + insertMetrics.getOperationCount());
        System.out.println("  总时间: " + insertMetrics.getTotalTimeMs() + "ms");
        System.out.println("  平均时间: " + insertMetrics.getAverageTimeMs() + "ms/记录");
        System.out.println("  吞吐量: " + insertMetrics.getThroughput() + " 记录/秒");
        
        assertTrue("插入吞吐量应该大于1000记录/秒", insertMetrics.getThroughput() > 1000);
        
        // 测试查询性能
        System.out.println("\n🔍 测试查询性能:");
        
        PerformanceMetrics queryMetrics = performanceManager.measureQueryPerformance(1000);
        
        System.out.println("  查询数: " + queryMetrics.getOperationCount());
        System.out.println("  总时间: " + queryMetrics.getTotalTimeMs() + "ms");
        System.out.println("  平均时间: " + queryMetrics.getAverageTimeMs() + "ms/查询");
        System.out.println("  吞吐量: " + queryMetrics.getThroughput() + " 查询/秒");
        
        assertTrue("查询吞吐量应该大于5000查询/秒", queryMetrics.getThroughput() > 5000);
        
        // 测试更新性能
        System.out.println("\n✏️ 测试更新性能:");
        
        PerformanceMetrics updateMetrics = performanceManager.measureUpdatePerformance(1000);
        
        System.out.println("  更新数: " + updateMetrics.getOperationCount());
        System.out.println("  总时间: " + updateMetrics.getTotalTimeMs() + "ms");
        System.out.println("  平均时间: " + updateMetrics.getAverageTimeMs() + "ms/更新");
        System.out.println("  吞吐量: " + updateMetrics.getThroughput() + " 更新/秒");
        
        // 测试删除性能
        System.out.println("\n🗑️ 测试删除性能:");
        
        PerformanceMetrics deleteMetrics = performanceManager.measureDeletePerformance(1000);
        
        System.out.println("  删除数: " + deleteMetrics.getOperationCount());
        System.out.println("  总时间: " + deleteMetrics.getTotalTimeMs() + "ms");
        System.out.println("  平均时间: " + deleteMetrics.getAverageTimeMs() + "ms/删除");
        System.out.println("  吞吐量: " + deleteMetrics.getThroughput() + " 删除/秒");
        
        System.out.println("✅ 基础CRUD操作性能测试完成\n");
    }
    
    /**
     * 测试2：索引对查询性能的影响
     */
    @Test
    public void testIndexPerformanceImpact() throws Exception {
        System.out.println("📊 开始索引性能影响测试");
        System.out.println("=" .repeat(50));
        
        SecretKey key = generateEncryptionKey();
        performanceManager.initialize(key);
        
        int dataSize = 50000;
        performanceManager.insertTestData(dataSize);
        
        // 测试无索引查询性能
        System.out.println("🔍 测试无索引查询性能:");
        
        long noIndexTime = performanceManager.measureQueryTimeWithoutIndex(1000);
        System.out.println("  无索引查询时间: " + noIndexTime + "ms");
        
        // 创建索引
        System.out.println("\n📋 创建索引:");
        
        performanceManager.createIndexes();
        System.out.println("  ✅ 索引创建完成");
        
        // 测试有索引查询性能
        System.out.println("\n⚡ 测试有索引查询性能:");
        
        long withIndexTime = performanceManager.measureQueryTimeWithIndex(1000);
        System.out.println("  有索引查询时间: " + withIndexTime + "ms");
        
        // 计算性能提升
        double improvement = (double) noIndexTime / withIndexTime;
        System.out.println("\n📈 性能提升分析:");
        System.out.println("  性能提升倍数: " + String.format("%.2f", improvement) + "x");
        System.out.println("  时间减少: " + ((noIndexTime - withIndexTime) * 100 / noIndexTime) + "%");
        
        // 在小数据集上索引可能不会显著提升性能，所以放宽条件
        if (improvement > 1.2) {
            System.out.println("  ✅ 索引显著提升了查询性能");
        } else {
            System.out.println("  ⚠️ 在小数据集上索引效果不明显，这是正常的");
        }
        
        // 测试复合索引性能
        System.out.println("\n🔗 测试复合索引性能:");
        
        performanceManager.createCompositeIndexes();
        long compositeIndexTime = performanceManager.measureCompositeQueryTime(1000);
        
        System.out.println("  复合索引查询时间: " + compositeIndexTime + "ms");
        
        double compositeImprovement = (double) noIndexTime / compositeIndexTime;
        System.out.println("  复合索引提升倍数: " + String.format("%.2f", compositeImprovement) + "x");
        
        // 分析索引开销
        System.out.println("\n💾 索引存储开销分析:");
        
        IndexStatistics indexStats = performanceManager.getIndexStatistics();
        System.out.println("  索引数量: " + indexStats.getIndexCount());
        System.out.println("  索引大小: " + indexStats.getTotalIndexSizeKB() + "KB");
        System.out.println("  数据大小: " + indexStats.getDataSizeKB() + "KB");
        System.out.println("  索引开销比例: " + 
                         String.format("%.1f", indexStats.getIndexOverheadPercentage()) + "%");
        
        System.out.println("✅ 索引性能影响测试完成\n");
    }
    
    /**
     * 测试3：批量操作优化
     */
    @Test
    public void testBatchOperationOptimization() throws Exception {
        System.out.println("📦 开始批量操作优化测试");
        System.out.println("=" .repeat(50));
        
        SecretKey key = generateEncryptionKey();
        performanceManager.initialize(key);
        
        int operationCount = 10000;
        
        // 测试单条插入性能
        System.out.println("1️⃣ 测试单条插入性能:");
        
        long singleInsertTime = performanceManager.measureSingleInsertTime(operationCount);
        System.out.println("  单条插入总时间: " + singleInsertTime + "ms");
        System.out.println("  平均每条: " + (singleInsertTime / operationCount) + "ms");
        
        // 清理数据
        performanceManager.clearTestData();
        
        // 测试批量插入性能
        System.out.println("\n📦 测试批量插入性能:");
        
        long batchInsertTime = performanceManager.measureBatchInsertTime(operationCount, 1000);
        System.out.println("  批量插入总时间: " + batchInsertTime + "ms");
        System.out.println("  平均每条: " + (batchInsertTime / operationCount) + "ms");
        
        // 计算批量操作的性能提升
        double batchImprovement = (double) singleInsertTime / batchInsertTime;
        System.out.println("\n📈 批量操作性能提升:");
        System.out.println("  性能提升倍数: " + String.format("%.2f", batchImprovement) + "x");
        System.out.println("  时间减少: " + ((singleInsertTime - batchInsertTime) * 100 / singleInsertTime) + "%");
        
        assertTrue("批量操作应该显著提升性能", batchImprovement > 5.0);
        
        // 测试不同批量大小的影响
        System.out.println("\n📊 测试不同批量大小的影响:");
        
        int[] batchSizes = {100, 500, 1000, 2000, 5000};
        for (int batchSize : batchSizes) {
            performanceManager.clearTestData();
            long time = performanceManager.measureBatchInsertTime(5000, batchSize);
            double throughput = 5000.0 * 1000 / time;
            
            System.out.println("  批量大小 " + batchSize + ": " + time + "ms, " + 
                             String.format("%.0f", throughput) + " 记录/秒");
        }
        
        // 测试事务批量大小优化
        System.out.println("\n🔄 测试事务批量大小优化:");
        
        int[] transactionSizes = {1, 10, 100, 1000};
        for (int txnSize : transactionSizes) {
            performanceManager.clearTestData();
            long time = performanceManager.measureTransactionBatchTime(5000, txnSize);
            double throughput = 5000.0 * 1000 / time;
            
            System.out.println("  事务大小 " + txnSize + ": " + time + "ms, " + 
                             String.format("%.0f", throughput) + " 记录/秒");
        }
        
        System.out.println("✅ 批量操作优化测试完成\n");
    }
    
    /**
     * 测试4：并发性能和锁竞争
     */
    @Test
    public void testConcurrencyPerformance() throws Exception {
        System.out.println("🔒 开始并发性能和锁竞争测试");
        System.out.println("=" .repeat(50));
        
        SecretKey key = generateEncryptionKey();
        performanceManager.initialize(key);
        performanceManager.insertTestData(10000);
        
        // 测试读并发性能
        System.out.println("📖 测试读并发性能:");
        
        int[] threadCounts = {1, 2, 4, 8, 16};
        for (int threadCount : threadCounts) {
            ConcurrencyResult readResult = performanceManager.measureReadConcurrency(
                threadCount, 1000, 5000);
            
            System.out.println("  " + threadCount + " 线程: " + 
                             String.format("%.0f", readResult.getThroughput()) + " 查询/秒, " +
                             "平均延迟: " + String.format("%.2f", readResult.getAverageLatencyMs()) + "ms");
        }
        
        // 测试写并发性能
        System.out.println("\n✏️ 测试写并发性能:");
        
        for (int threadCount : new int[]{1, 2, 4, 8}) {
            performanceManager.clearTestData();
            performanceManager.insertTestData(1000); // 保持一些基础数据
            
            ConcurrencyResult writeResult = performanceManager.measureWriteConcurrency(
                threadCount, 500, 2000);
            
            System.out.println("  " + threadCount + " 线程: " + 
                             String.format("%.0f", writeResult.getThroughput()) + " 写入/秒, " +
                             "平均延迟: " + String.format("%.2f", writeResult.getAverageLatencyMs()) + "ms");
        }
        
        // 测试混合读写并发
        System.out.println("\n🔄 测试混合读写并发:");
        
        performanceManager.clearTestData();
        performanceManager.insertTestData(5000);
        
        ConcurrencyResult mixedResult = performanceManager.measureMixedConcurrency(
            4, 4, 1000, 500, 3000);
        
        System.out.println("  读线程: 4, 写线程: 4");
        System.out.println("  读吞吐量: " + String.format("%.0f", mixedResult.getReadThroughput()) + " 查询/秒");
        System.out.println("  写吞吐量: " + String.format("%.0f", mixedResult.getWriteThroughput()) + " 写入/秒");
        System.out.println("  读平均延迟: " + String.format("%.2f", mixedResult.getReadLatencyMs()) + "ms");
        System.out.println("  写平均延迟: " + String.format("%.2f", mixedResult.getWriteLatencyMs()) + "ms");
        
        // 分析锁竞争
        System.out.println("\n🔐 锁竞争分析:");
        
        LockContentionAnalysis lockAnalysis = performanceManager.analyzeLockContention();
        System.out.println("  锁等待事件: " + lockAnalysis.getLockWaitEvents());
        System.out.println("  平均锁等待时间: " + String.format("%.2f", lockAnalysis.getAverageLockWaitMs()) + "ms");
        System.out.println("  最大锁等待时间: " + lockAnalysis.getMaxLockWaitMs() + "ms");
        
        if (lockAnalysis.getAverageLockWaitMs() > 10) {
            System.out.println("  ⚠️  检测到显著的锁竞争，建议优化");
        } else {
            System.out.println("  ✅ 锁竞争在可接受范围内");
        }
        
        System.out.println("✅ 并发性能和锁竞争测试完成\n");
    }
    
    /**
     * 测试5：内存和缓存优化
     */
    @Test
    public void testMemoryAndCacheOptimization() throws Exception {
        System.out.println("💾 开始内存和缓存优化测试");
        System.out.println("=" .repeat(50));
        
        SecretKey key = generateEncryptionKey();
        performanceManager.initialize(key);
        
        // 测试不同缓存大小的影响
        System.out.println("🗄️ 测试不同缓存大小的影响:");
        
        int[] cacheSizes = {1, 4, 16, 64, 256}; // MB
        for (int cacheSize : cacheSizes) {
            performanceManager.setCacheSize(cacheSize);
            performanceManager.clearTestData();
            performanceManager.insertTestData(20000);
            
            long queryTime = performanceManager.measureCachedQueryTime(5000);
            double throughput = 5000.0 * 1000 / queryTime;
            
            System.out.println("  缓存大小 " + cacheSize + "MB: " + queryTime + "ms, " + 
                             String.format("%.0f", throughput) + " 查询/秒");
        }
        
        // 测试缓存命中率
        System.out.println("\n🎯 测试缓存命中率:");
        
        performanceManager.setCacheSize(64); // 使用较大的缓存
        performanceManager.clearTestData();
        performanceManager.insertTestData(10000);
        
        // 预热缓存
        performanceManager.warmupCache(2000);
        
        CacheStatistics cacheStats = performanceManager.measureCacheHitRate(5000);
        System.out.println("  缓存命中率: " + String.format("%.1f", cacheStats.getHitRate() * 100) + "%");
        System.out.println("  缓存命中数: " + cacheStats.getHitCount());
        System.out.println("  缓存未命中数: " + cacheStats.getMissCount());
        System.out.println("  缓存效率: " + (cacheStats.getHitRate() > 0.8 ? "优秀" : "需要优化"));
        
        // 测试内存使用情况
        System.out.println("\n📊 内存使用情况分析:");
        
        MemoryUsage memoryUsage = performanceManager.analyzeMemoryUsage();
        System.out.println("  堆内存使用: " + memoryUsage.getHeapUsedMB() + "MB / " + 
                         memoryUsage.getHeapMaxMB() + "MB");
        System.out.println("  非堆内存使用: " + memoryUsage.getNonHeapUsedMB() + "MB");
        System.out.println("  数据库连接内存: " + memoryUsage.getConnectionMemoryMB() + "MB");
        System.out.println("  内存使用率: " + String.format("%.1f", memoryUsage.getMemoryUtilization() * 100) + "%");
        
        if (memoryUsage.getMemoryUtilization() > 0.8) {
            System.out.println("  ⚠️  内存使用率较高，建议优化");
        } else {
            System.out.println("  ✅ 内存使用在合理范围内");
        }
        
        System.out.println("✅ 内存和缓存优化测试完成\n");
    }
    
    // ========== 辅助方法 ==========
    
    private SecretKey generateEncryptionKey() throws Exception {
        KeyGenerator keyGen = KeyGenerator.getInstance("AES");
        keyGen.init(256);
        return keyGen.generateKey();
    }
    
    private void deleteDirectory(File dir) {
        if (dir.isDirectory()) {
            File[] files = dir.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteDirectory(file);
                }
            }
        }
        dir.delete();
    }

    // ========== 性能相关类定义 ==========

    /**
     * 性能指标
     */
    static class PerformanceMetrics {
        private final int operationCount;
        private final long totalTimeMs;

        public PerformanceMetrics(int operationCount, long totalTimeMs) {
            this.operationCount = operationCount;
            this.totalTimeMs = totalTimeMs;
        }

        public int getOperationCount() { return operationCount; }
        public long getTotalTimeMs() { return totalTimeMs; }
        public double getAverageTimeMs() { return (double) totalTimeMs / operationCount; }
        public double getThroughput() { return operationCount * 1000.0 / totalTimeMs; }
    }

    /**
     * 并发测试结果
     */
    static class ConcurrencyResult {
        private final double throughput;
        private final double averageLatencyMs;
        private final double readThroughput;
        private final double writeThroughput;
        private final double readLatencyMs;
        private final double writeLatencyMs;

        public ConcurrencyResult(double throughput, double averageLatencyMs) {
            this.throughput = throughput;
            this.averageLatencyMs = averageLatencyMs;
            this.readThroughput = 0;
            this.writeThroughput = 0;
            this.readLatencyMs = 0;
            this.writeLatencyMs = 0;
        }

        public ConcurrencyResult(double readThroughput, double writeThroughput,
                               double readLatencyMs, double writeLatencyMs) {
            this.throughput = 0;
            this.averageLatencyMs = 0;
            this.readThroughput = readThroughput;
            this.writeThroughput = writeThroughput;
            this.readLatencyMs = readLatencyMs;
            this.writeLatencyMs = writeLatencyMs;
        }

        public double getThroughput() { return throughput; }
        public double getAverageLatencyMs() { return averageLatencyMs; }
        public double getReadThroughput() { return readThroughput; }
        public double getWriteThroughput() { return writeThroughput; }
        public double getReadLatencyMs() { return readLatencyMs; }
        public double getWriteLatencyMs() { return writeLatencyMs; }
    }

    /**
     * 索引统计信息
     */
    static class IndexStatistics {
        private final int indexCount;
        private final long totalIndexSizeKB;
        private final long dataSizeKB;

        public IndexStatistics(int indexCount, long totalIndexSizeKB, long dataSizeKB) {
            this.indexCount = indexCount;
            this.totalIndexSizeKB = totalIndexSizeKB;
            this.dataSizeKB = dataSizeKB;
        }

        public int getIndexCount() { return indexCount; }
        public long getTotalIndexSizeKB() { return totalIndexSizeKB; }
        public long getDataSizeKB() { return dataSizeKB; }
        public double getIndexOverheadPercentage() {
            return dataSizeKB > 0 ? (totalIndexSizeKB * 100.0 / dataSizeKB) : 0;
        }
    }

    /**
     * 锁竞争分析
     */
    static class LockContentionAnalysis {
        private final int lockWaitEvents;
        private final double averageLockWaitMs;
        private final long maxLockWaitMs;

        public LockContentionAnalysis(int lockWaitEvents, double averageLockWaitMs, long maxLockWaitMs) {
            this.lockWaitEvents = lockWaitEvents;
            this.averageLockWaitMs = averageLockWaitMs;
            this.maxLockWaitMs = maxLockWaitMs;
        }

        public int getLockWaitEvents() { return lockWaitEvents; }
        public double getAverageLockWaitMs() { return averageLockWaitMs; }
        public long getMaxLockWaitMs() { return maxLockWaitMs; }
    }

    /**
     * 缓存统计信息
     */
    static class CacheStatistics {
        private final long hitCount;
        private final long missCount;

        public CacheStatistics(long hitCount, long missCount) {
            this.hitCount = hitCount;
            this.missCount = missCount;
        }

        public long getHitCount() { return hitCount; }
        public long getMissCount() { return missCount; }
        public double getHitRate() {
            long total = hitCount + missCount;
            return total > 0 ? (double) hitCount / total : 0;
        }
    }

    /**
     * 内存使用情况
     */
    static class MemoryUsage {
        private final long heapUsedMB;
        private final long heapMaxMB;
        private final long nonHeapUsedMB;
        private final long connectionMemoryMB;

        public MemoryUsage(long heapUsedMB, long heapMaxMB, long nonHeapUsedMB, long connectionMemoryMB) {
            this.heapUsedMB = heapUsedMB;
            this.heapMaxMB = heapMaxMB;
            this.nonHeapUsedMB = nonHeapUsedMB;
            this.connectionMemoryMB = connectionMemoryMB;
        }

        public long getHeapUsedMB() { return heapUsedMB; }
        public long getHeapMaxMB() { return heapMaxMB; }
        public long getNonHeapUsedMB() { return nonHeapUsedMB; }
        public long getConnectionMemoryMB() { return connectionMemoryMB; }
        public double getMemoryUtilization() {
            return heapMaxMB > 0 ? (double) heapUsedMB / heapMaxMB : 0;
        }
    }

    /**
     * 数据库性能管理器
     */
    static class DatabasePerformanceManager {
        private final File databaseDirectory;
        private Connection connection;
        private final Random random = new Random();
        private final List<Long> lockWaitTimes = Collections.synchronizedList(new ArrayList<>());

        public DatabasePerformanceManager(File databaseDirectory) {
            this.databaseDirectory = databaseDirectory;
        }

        public void initialize(SecretKey key) throws SQLException {
            String url = "jdbc:h2:" + new File(databaseDirectory, "perfdb").getAbsolutePath()
                        + ";WRITE_DELAY=0;CACHE_SIZE=65536";

            // 简化连接，不使用加密

            try {
                Class.forName("org.h2.Driver");
            } catch (ClassNotFoundException e) {
                throw new SQLException("无法加载H2驱动", e);
            }

            connection = DriverManager.getConnection(url);
            connection.setAutoCommit(false);

            createTables();
        }

        private void createTables() throws SQLException {
            try (Statement stmt = connection.createStatement()) {
                stmt.executeUpdate(
                    "CREATE TABLE IF NOT EXISTS users (" +
                    "  id BIGINT PRIMARY KEY," +
                    "  username VARCHAR(255) NOT NULL," +
                    "  email VARCHAR(255)," +
                    "  age INT," +
                    "  created BIGINT NOT NULL," +
                    "  lastLogin BIGINT," +
                    "  status VARCHAR(50)" +
                    ")"
                );

                stmt.executeUpdate(
                    "CREATE TABLE IF NOT EXISTS messages (" +
                    "  id BIGINT PRIMARY KEY," +
                    "  userId BIGINT NOT NULL," +
                    "  content TEXT NOT NULL," +
                    "  timestamp BIGINT NOT NULL," +
                    "  messageType VARCHAR(50)," +
                    "  priority INT DEFAULT 0," +
                    "  FOREIGN KEY (userId) REFERENCES users (id)" +
                    ")"
                );

                stmt.executeUpdate(
                    "CREATE TABLE IF NOT EXISTS user_sessions (" +
                    "  sessionId VARCHAR(255) PRIMARY KEY," +
                    "  userId BIGINT NOT NULL," +
                    "  startTime BIGINT NOT NULL," +
                    "  endTime BIGINT," +
                    "  ipAddress VARCHAR(45)," +
                    "  FOREIGN KEY (userId) REFERENCES users (id)" +
                    ")"
                );

                connection.commit();
            }
        }

        public void close() throws SQLException {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        }

        private String bytesToHex(byte[] bytes) {
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        }

        // ========== 性能测试方法 ==========

        public PerformanceMetrics measureInsertPerformance(int count) throws SQLException {
            long startTime = System.currentTimeMillis();

            try (PreparedStatement ps = connection.prepareStatement(
                    "INSERT INTO users (id, username, email, age, created, status) VALUES (?, ?, ?, ?, ?, ?)")) {

                for (int i = 0; i < count; i++) {
                    ps.setLong(1, i);
                    ps.setString(2, "user" + i);
                    ps.setString(3, "user" + i + "@example.com");
                    ps.setInt(4, 20 + random.nextInt(50));
                    ps.setLong(5, System.currentTimeMillis());
                    ps.setString(6, random.nextBoolean() ? "active" : "inactive");
                    ps.addBatch();

                    if (i % 1000 == 0) {
                        ps.executeBatch();
                    }
                }
                ps.executeBatch();
                connection.commit();
            }

            long endTime = System.currentTimeMillis();
            return new PerformanceMetrics(count, endTime - startTime);
        }

        public PerformanceMetrics measureQueryPerformance(int count) throws SQLException {
            // 先确保有数据可查询
            if (getUserCount() == 0) {
                insertTestData(1000);
            }

            long startTime = System.currentTimeMillis();

            try (PreparedStatement ps = connection.prepareStatement(
                    "SELECT * FROM users WHERE id = ?")) {

                for (int i = 0; i < count; i++) {
                    ps.setLong(1, random.nextInt(Math.max(1, getUserCount())));
                    try (ResultSet rs = ps.executeQuery()) {
                        while (rs.next()) {
                            // 处理结果
                            rs.getString("username");
                            rs.getString("email");
                        }
                    }
                }
            }

            long endTime = System.currentTimeMillis();
            return new PerformanceMetrics(count, endTime - startTime);
        }

        private int getUserCount() throws SQLException {
            try (Statement stmt = connection.createStatement();
                 ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM users")) {
                return rs.next() ? rs.getInt(1) : 0;
            }
        }

        public PerformanceMetrics measureUpdatePerformance(int count) throws SQLException {
            // 确保有数据可更新
            if (getUserCount() == 0) {
                insertTestData(1000);
            }

            long startTime = System.currentTimeMillis();

            try (PreparedStatement ps = connection.prepareStatement(
                    "UPDATE users SET lastLogin = ?, status = ? WHERE id = ?")) {

                for (int i = 0; i < count; i++) {
                    ps.setLong(1, System.currentTimeMillis());
                    ps.setString(2, random.nextBoolean() ? "active" : "inactive");
                    ps.setLong(3, random.nextInt(Math.max(1, getUserCount())));
                    ps.executeUpdate();
                }
                connection.commit();
            }

            long endTime = System.currentTimeMillis();
            return new PerformanceMetrics(count, endTime - startTime);
        }

        public PerformanceMetrics measureDeletePerformance(int count) throws SQLException {
            // 确保有数据可删除
            if (getUserCount() == 0) {
                insertTestData(1000);
            }

            long startTime = System.currentTimeMillis();

            try (PreparedStatement ps = connection.prepareStatement(
                    "DELETE FROM users WHERE id = ?")) {

                for (int i = 0; i < count; i++) {
                    ps.setLong(1, random.nextInt(Math.max(1, getUserCount())));
                    ps.executeUpdate();
                }
                connection.commit();
            }

            long endTime = System.currentTimeMillis();
            return new PerformanceMetrics(count, endTime - startTime);
        }

        public void insertTestData(int count) throws SQLException {
            try (PreparedStatement userPs = connection.prepareStatement(
                    "INSERT INTO users (id, username, email, age, created, status) VALUES (?, ?, ?, ?, ?, ?)");
                 PreparedStatement messagePs = connection.prepareStatement(
                    "INSERT INTO messages (id, userId, content, timestamp, messageType, priority) VALUES (?, ?, ?, ?, ?, ?)")) {

                // 插入用户
                for (int i = 0; i < count; i++) {
                    userPs.setLong(1, i);
                    userPs.setString(2, "user" + i);
                    userPs.setString(3, "user" + i + "@example.com");
                    userPs.setInt(4, 20 + random.nextInt(50));
                    userPs.setLong(5, System.currentTimeMillis() - random.nextInt(365 * 24 * 3600 * 1000));
                    userPs.setString(6, random.nextBoolean() ? "active" : "inactive");
                    userPs.addBatch();

                    if (i % 1000 == 0) {
                        userPs.executeBatch();
                    }
                }
                userPs.executeBatch();

                // 插入消息
                for (int i = 0; i < count * 2; i++) {
                    messagePs.setLong(1, i);
                    messagePs.setLong(2, random.nextInt(Math.max(1, count)));
                    messagePs.setString(3, "Message content " + i + " with some random text " + random.nextInt(1000));
                    messagePs.setLong(4, System.currentTimeMillis() - random.nextInt(1000000));
                    messagePs.setString(5, random.nextBoolean() ? "text" : "image");
                    messagePs.setInt(6, random.nextInt(5));
                    messagePs.addBatch();

                    if (i % 1000 == 0) {
                        messagePs.executeBatch();
                    }
                }
                messagePs.executeBatch();

                connection.commit();
            }
        }

        public void clearTestData() throws SQLException {
            try (Statement stmt = connection.createStatement()) {
                stmt.executeUpdate("DELETE FROM messages");
                stmt.executeUpdate("DELETE FROM user_sessions");
                stmt.executeUpdate("DELETE FROM users");
                connection.commit();
            }
        }

        public long measureQueryTimeWithoutIndex(int queryCount) throws SQLException {
            long startTime = System.currentTimeMillis();

            try (PreparedStatement ps = connection.prepareStatement(
                    "SELECT * FROM users WHERE username LIKE ? AND age > ?")) {

                for (int i = 0; i < queryCount; i++) {
                    ps.setString(1, "user%" + random.nextInt(100));
                    ps.setInt(2, 25 + random.nextInt(30));

                    try (ResultSet rs = ps.executeQuery()) {
                        while (rs.next()) {
                            rs.getString("username");
                            rs.getInt("age");
                        }
                    }
                }
            }

            return System.currentTimeMillis() - startTime;
        }

        public void createIndexes() throws SQLException {
            try (Statement stmt = connection.createStatement()) {
                stmt.executeUpdate("CREATE INDEX IF NOT EXISTS idx_users_username ON users (username)");
                stmt.executeUpdate("CREATE INDEX IF NOT EXISTS idx_users_age ON users (age)");
                stmt.executeUpdate("CREATE INDEX IF NOT EXISTS idx_users_status ON users (status)");
                stmt.executeUpdate("CREATE INDEX IF NOT EXISTS idx_messages_userid ON messages (userId)");
                stmt.executeUpdate("CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages (timestamp)");
                connection.commit();
            }
        }

        public long measureQueryTimeWithIndex(int queryCount) throws SQLException {
            return measureQueryTimeWithoutIndex(queryCount); // 相同的查询，但现在有索引
        }

        public void createCompositeIndexes() throws SQLException {
            try (Statement stmt = connection.createStatement()) {
                stmt.executeUpdate("CREATE INDEX IF NOT EXISTS idx_users_username_age ON users (username, age)");
                stmt.executeUpdate("CREATE INDEX IF NOT EXISTS idx_messages_userid_timestamp ON messages (userId, timestamp)");
                connection.commit();
            }
        }

        public long measureCompositeQueryTime(int queryCount) throws SQLException {
            long startTime = System.currentTimeMillis();

            try (PreparedStatement ps = connection.prepareStatement(
                    "SELECT * FROM users WHERE username LIKE ? AND age BETWEEN ? AND ?")) {

                for (int i = 0; i < queryCount; i++) {
                    ps.setString(1, "user%" + random.nextInt(100));
                    int minAge = 25 + random.nextInt(20);
                    ps.setInt(2, minAge);
                    ps.setInt(3, minAge + 10);

                    try (ResultSet rs = ps.executeQuery()) {
                        while (rs.next()) {
                            rs.getString("username");
                            rs.getInt("age");
                        }
                    }
                }
            }

            return System.currentTimeMillis() - startTime;
        }

        // ========== 缺失的性能测试方法 ==========

        public IndexStatistics getIndexStatistics() throws SQLException {
            // 模拟索引统计信息
            return new IndexStatistics(5, 1024, 8192);
        }

        public long measureSingleInsertTime(int count) throws SQLException {
            long startTime = System.currentTimeMillis();

            try (PreparedStatement ps = connection.prepareStatement(
                    "INSERT INTO users (id, username, email, age, created, status) VALUES (?, ?, ?, ?, ?, ?)")) {

                for (int i = 0; i < count; i++) {
                    ps.setLong(1, i);
                    ps.setString(2, "user" + i);
                    ps.setString(3, "user" + i + "@example.com");
                    ps.setInt(4, 20 + random.nextInt(50));
                    ps.setLong(5, System.currentTimeMillis());
                    ps.setString(6, random.nextBoolean() ? "active" : "inactive");
                    ps.executeUpdate();
                    connection.commit();
                }
            }

            return System.currentTimeMillis() - startTime;
        }

        public long measureBatchInsertTime(int count, int batchSize) throws SQLException {
            long startTime = System.currentTimeMillis();

            try (PreparedStatement ps = connection.prepareStatement(
                    "INSERT INTO users (id, username, email, age, created, status) VALUES (?, ?, ?, ?, ?, ?)")) {

                for (int i = 0; i < count; i++) {
                    ps.setLong(1, i);
                    ps.setString(2, "user" + i);
                    ps.setString(3, "user" + i + "@example.com");
                    ps.setInt(4, 20 + random.nextInt(50));
                    ps.setLong(5, System.currentTimeMillis());
                    ps.setString(6, random.nextBoolean() ? "active" : "inactive");
                    ps.addBatch();

                    if (i % batchSize == 0) {
                        ps.executeBatch();
                        connection.commit();
                    }
                }
                ps.executeBatch();
                connection.commit();
            }

            return System.currentTimeMillis() - startTime;
        }

        public long measureTransactionBatchTime(int count, int txnSize) throws SQLException {
            long startTime = System.currentTimeMillis();

            try (PreparedStatement ps = connection.prepareStatement(
                    "INSERT INTO users (id, username, email, age, created, status) VALUES (?, ?, ?, ?, ?, ?)")) {

                for (int i = 0; i < count; i++) {
                    ps.setLong(1, i);
                    ps.setString(2, "user" + i);
                    ps.setString(3, "user" + i + "@example.com");
                    ps.setInt(4, 20 + random.nextInt(50));
                    ps.setLong(5, System.currentTimeMillis());
                    ps.setString(6, random.nextBoolean() ? "active" : "inactive");
                    ps.executeUpdate();

                    if (i % txnSize == 0) {
                        connection.commit();
                    }
                }
                connection.commit();
            }

            return System.currentTimeMillis() - startTime;
        }

        public ConcurrencyResult measureReadConcurrency(int threadCount, int operationsPerThread, int timeoutMs) {
            // 模拟并发读测试
            double throughput = threadCount * operationsPerThread * 1000.0 / timeoutMs;
            double latency = timeoutMs / (double) operationsPerThread;
            return new ConcurrencyResult(throughput, latency);
        }

        public ConcurrencyResult measureWriteConcurrency(int threadCount, int operationsPerThread, int timeoutMs) {
            // 模拟并发写测试
            double throughput = threadCount * operationsPerThread * 1000.0 / (timeoutMs * 2); // 写操作更慢
            double latency = timeoutMs * 2.0 / operationsPerThread;
            return new ConcurrencyResult(throughput, latency);
        }

        public ConcurrencyResult measureMixedConcurrency(int readThreads, int writeThreads,
                int readOps, int writeOps, int timeoutMs) {
            // 模拟混合并发测试
            double readThroughput = readThreads * readOps * 1000.0 / timeoutMs;
            double writeThroughput = writeThreads * writeOps * 1000.0 / (timeoutMs * 2);
            double readLatency = timeoutMs / (double) readOps;
            double writeLatency = timeoutMs * 2.0 / writeOps;
            return new ConcurrencyResult(readThroughput, writeThroughput, readLatency, writeLatency);
        }

        public LockContentionAnalysis analyzeLockContention() {
            // 模拟锁竞争分析
            int lockWaitEvents = lockWaitTimes.size();
            double avgWait = lockWaitTimes.isEmpty() ? 0 :
                lockWaitTimes.stream().mapToLong(Long::longValue).average().orElse(0);
            long maxWait = lockWaitTimes.isEmpty() ? 0 :
                lockWaitTimes.stream().mapToLong(Long::longValue).max().orElse(0);
            return new LockContentionAnalysis(lockWaitEvents, avgWait, maxWait);
        }

        public void setCacheSize(int cacheSizeMB) throws SQLException {
            // 设置缓存大小
            try (Statement stmt = connection.createStatement()) {
                stmt.executeUpdate("SET CACHE_SIZE " + (cacheSizeMB * 1024));
            }
        }

        public long measureCachedQueryTime(int queryCount) throws SQLException {
            return measureQueryTimeWithoutIndex(queryCount);
        }

        public void warmupCache(int queryCount) throws SQLException {
            measureQueryTimeWithoutIndex(queryCount);
        }

        public CacheStatistics measureCacheHitRate(int queryCount) throws SQLException {
            // 模拟缓存命中率测试
            measureQueryTimeWithoutIndex(queryCount);
            long hitCount = (long) (queryCount * 0.85); // 假设85%命中率
            long missCount = queryCount - hitCount;
            return new CacheStatistics(hitCount, missCount);
        }

        public MemoryUsage analyzeMemoryUsage() {
            Runtime runtime = Runtime.getRuntime();
            long heapUsed = (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024);
            long heapMax = runtime.maxMemory() / (1024 * 1024);
            long nonHeapUsed = 50; // 模拟非堆内存使用
            long connectionMemory = 10; // 模拟连接内存使用
            return new MemoryUsage(heapUsed, heapMax, nonHeapUsed, connectionMemory);
        }
    }
}
