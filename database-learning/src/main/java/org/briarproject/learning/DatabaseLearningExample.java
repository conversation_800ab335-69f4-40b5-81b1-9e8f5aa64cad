package org.briarproject.learning;

import org.briarproject.bramble.api.db.Database;
import org.briarproject.bramble.api.db.DatabaseComponent;
import org.briarproject.bramble.api.db.Transaction;
import org.briarproject.bramble.api.db.TransactionManager;
import org.briarproject.bramble.api.contact.Contact;
import org.briarproject.bramble.api.contact.ContactId;
import org.briarproject.bramble.api.identity.Author;
import org.briarproject.bramble.api.identity.AuthorId;
import org.briarproject.bramble.api.crypto.CryptoComponent;
import org.briarproject.bramble.api.crypto.KeyPair;
import org.briarproject.bramble.api.crypto.PrivateKey;
import org.briarproject.bramble.api.crypto.PublicKey;

import java.util.Collection;
import java.util.logging.Logger;

/**
 * 数据库学习示例
 * 
 * 这个类演示了如何在Briar项目中使用数据库：
 * 1. 数据库事务管理
 * 2. 联系人数据的存储和检索
 * 3. 作者身份的管理
 * 4. 数据库组件的使用
 */
public class DatabaseLearningExample {
    
    private static final Logger LOG = Logger.getLogger(DatabaseLearningExample.class.getName());
    
    private final DatabaseComponent databaseComponent;
    private final TransactionManager transactionManager;
    private final CryptoComponent cryptoComponent;
    
    public DatabaseLearningExample(DatabaseComponent databaseComponent,
                                 TransactionManager transactionManager,
                                 CryptoComponent cryptoComponent) {
        this.databaseComponent = databaseComponent;
        this.transactionManager = transactionManager;
        this.cryptoComponent = cryptoComponent;
    }
    
    /**
     * 演示基本的数据库操作
     */
    public void demonstrateBasicDatabaseOperations() throws Exception {
        LOG.info("开始演示基本数据库操作...");
        
        // 1. 创建一个新的作者身份
        Author author = createAuthor("Alice", "<EMAIL>");
        LOG.info("创建作者: " + author.getName());
        
        // 2. 在事务中存储作者
        transactionManager.transaction(false, txn -> {
            databaseComponent.addLocalAuthor(txn, author);
            LOG.info("作者已存储到数据库");
            return null;
        });
        
        // 3. 读取所有本地作者
        Collection<Author> authors = transactionManager.transactionWithResult(true, txn -> {
            return databaseComponent.getLocalAuthors(txn);
        });
        
        LOG.info("数据库中的作者数量: " + authors.size());
        for (Author a : authors) {
            LOG.info("作者: " + a.getName() + " (ID: " + a.getId() + ")");
        }
    }
    
    /**
     * 演示联系人管理
     */
    public void demonstrateContactManagement() throws Exception {
        LOG.info("开始演示联系人管理...");
        
        // 1. 创建本地作者
        Author localAuthor = createAuthor("Bob", "<EMAIL>");
        
        // 2. 创建远程联系人作者
        Author remoteAuthor = createAuthor("Charlie", "<EMAIL>");
        
        // 3. 在事务中添加作者和联系人
        transactionManager.transaction(false, txn -> {
            // 添加本地作者
            databaseComponent.addLocalAuthor(txn, localAuthor);
            
            // 创建联系人关系
            Contact contact = new Contact(new ContactId(1), remoteAuthor, localAuthor.getId(), true, true);
            databaseComponent.addContact(txn, contact);
            
            LOG.info("联系人关系已建立: " + localAuthor.getName() + " <-> " + remoteAuthor.getName());
            return null;
        });
        
        // 4. 查询联系人
        Collection<Contact> contacts = transactionManager.transactionWithResult(true, txn -> {
            return databaseComponent.getContacts(txn);
        });
        
        LOG.info("联系人数量: " + contacts.size());
        for (Contact contact : contacts) {
            LOG.info("联系人: " + contact.getAuthor().getName() + 
                    " (验证状态: " + contact.isVerified() + ")");
        }
    }
    
    /**
     * 演示事务管理的最佳实践
     */
    public void demonstrateTransactionBestPractices() throws Exception {
        LOG.info("开始演示事务管理最佳实践...");
        
        // 1. 只读事务示例
        int authorCount = transactionManager.transactionWithResult(true, txn -> {
            Collection<Author> authors = databaseComponent.getLocalAuthors(txn);
            return authors.size();
        });
        LOG.info("只读事务 - 作者数量: " + authorCount);
        
        // 2. 写事务示例
        Author newAuthor = createAuthor("David", "<EMAIL>");
        transactionManager.transaction(false, txn -> {
            databaseComponent.addLocalAuthor(txn, newAuthor);
            LOG.info("写事务 - 添加新作者: " + newAuthor.getName());
            return null;
        });
        
        // 3. 事务回滚示例（模拟错误情况）
        try {
            transactionManager.transaction(false, txn -> {
                Author tempAuthor = createAuthor("Temp", "<EMAIL>");
                databaseComponent.addLocalAuthor(txn, tempAuthor);
                
                // 模拟错误，导致事务回滚
                throw new RuntimeException("模拟错误 - 事务将回滚");
            });
        } catch (Exception e) {
            LOG.info("事务回滚示例 - 错误被捕获: " + e.getMessage());
        }
        
        // 验证回滚是否生效
        int finalCount = transactionManager.transactionWithResult(true, txn -> {
            Collection<Author> authors = databaseComponent.getLocalAuthors(txn);
            return authors.size();
        });
        LOG.info("事务回滚后的作者数量: " + finalCount);
    }
    
    /**
     * 创建一个新的作者身份
     */
    private Author createAuthor(String name, String email) {
        // 生成密钥对
        KeyPair keyPair = cryptoComponent.generateSignatureKeyPair();
        PublicKey publicKey = keyPair.getPublic();
        
        // 创建作者ID
        AuthorId authorId = new AuthorId(cryptoComponent.hash(publicKey.getEncoded()));
        
        // 创建作者对象
        return new Author(authorId, name, publicKey);
    }
    
    /**
     * 演示数据库性能监控
     */
    public void demonstratePerformanceMonitoring() throws Exception {
        LOG.info("开始演示数据库性能监控...");
        
        long startTime = System.currentTimeMillis();
        
        // 批量操作示例
        for (int i = 0; i < 10; i++) {
            final int index = i;
            transactionManager.transaction(false, txn -> {
                Author author = createAuthor("User" + index, "user" + index + "@example.com");
                databaseComponent.addLocalAuthor(txn, author);
                return null;
            });
        }
        
        long endTime = System.currentTimeMillis();
        LOG.info("批量添加10个作者耗时: " + (endTime - startTime) + "ms");
        
        // 查询性能测试
        startTime = System.currentTimeMillis();
        Collection<Author> allAuthors = transactionManager.transactionWithResult(true, txn -> {
            return databaseComponent.getLocalAuthors(txn);
        });
        endTime = System.currentTimeMillis();
        
        LOG.info("查询所有作者(" + allAuthors.size() + "个)耗时: " + (endTime - startTime) + "ms");
    }
    
    /**
     * 主方法 - 运行所有演示
     */
    public static void main(String[] args) {
        LOG.info("=== Briar数据库学习示例 ===");
        
        // 注意：在实际使用中，需要通过依赖注入获取这些组件
        // 这里只是演示代码结构
        LOG.info("请注意：此示例需要在完整的Briar环境中运行");
        LOG.info("需要正确配置DatabaseComponent、TransactionManager和CryptoComponent");
        
        /*
        // 实际运行代码（需要在Briar环境中）
        DatabaseLearningExample example = new DatabaseLearningExample(
            databaseComponent, transactionManager, cryptoComponent);
        
        try {
            example.demonstrateBasicDatabaseOperations();
            example.demonstrateContactManagement();
            example.demonstrateTransactionBestPractices();
            example.demonstratePerformanceMonitoring();
        } catch (Exception e) {
            LOG.severe("演示过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
        */
    }
}
