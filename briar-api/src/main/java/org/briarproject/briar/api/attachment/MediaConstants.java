package org.briarproject.briar.api.attachment;

import static org.briarproject.bramble.api.sync.SyncConstants.MAX_MESSAGE_BODY_LENGTH;

public interface MediaConstants {

	// Metadata keys for messages
	String MSG_KEY_CONTENT_TYPE = "contentType";
	String MSG_KEY_DESCRIPTOR_LENGTH = "descriptorLength";

	/**
	 * The maximum length of an attachment's content type in UTF-8 bytes.
	 */
	int MAX_CONTENT_TYPE_BYTES = 80;

	/**
	 * The maximum allowed size of image attachments.
	 * TODO: Different limit for GIFs?
	 */
	int MAX_IMAGE_SIZE = MAX_MESSAGE_BODY_LENGTH - 100; // 6 * 1024 * 1024;

	/**
	 * The maximum allowed size of audio attachments (voice messages).
	 * Set to 2MB to allow for reasonable quality voice recordings.
	 */
	int MAX_AUDIO_SIZE = 2 * 1024 * 1024;

	/**
	 * The maximum duration for voice messages in milliseconds (5 minutes).
	 */
	long MAX_VOICE_MESSAGE_DURATION_MS = 5 * 60 * 1000;

	/**
	 * Supported audio content types for voice messages.
	 */
	String CONTENT_TYPE_AUDIO_AAC = "audio/aac";
	String CONTENT_TYPE_AUDIO_MP4 = "audio/mp4";
	String CONTENT_TYPE_AUDIO_3GPP = "audio/3gpp";
}
