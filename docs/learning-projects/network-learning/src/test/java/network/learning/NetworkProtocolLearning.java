package network.learning;

import org.junit.Test;
import org.junit.Before;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.io.*;
import java.nio.ByteBuffer;
import static org.junit.Assert.*;

/**
 * 网络协议学习实践
 * 
 * 学习目标：
 * 1. 理解Briar的网络协议设计
 * 2. 掌握帧结构和消息格式
 * 3. 学习连接建立和维护机制
 * 4. 实现简化版的网络协议
 */
public class NetworkProtocolLearning {
    
    private ProtocolManager protocolManager;
    private ConnectionManager connectionManager;
    
    @Before
    public void setUp() {
        protocolManager = new ProtocolManager();
        connectionManager = new ConnectionManager();
    }
    
    /**
     * 测试1：协议帧结构
     */
    @Test
    public void testProtocolFrameStructure() {
        System.out.println("📦 开始协议帧结构测试");
        System.out.println("=" .repeat(50));
        
        // 创建不同类型的帧
        System.out.println("🔧 创建不同类型的协议帧:");
        
        // 1. 握手帧
        HandshakeFrame handshake = new HandshakeFrame(
            ProtocolVersion.V4, 
            "alice_public_key".getBytes(),
            System.currentTimeMillis()
        );
        
        byte[] handshakeData = handshake.serialize();
        System.out.println("  ✅ 握手帧: " + handshakeData.length + " 字节");
        System.out.println("     版本: " + handshake.getVersion());
        System.out.println("     公钥长度: " + handshake.getPublicKey().length);
        
        // 2. 数据帧
        DataFrame dataFrame = new DataFrame(
            1,  // 序列号
            "Hello, Briar Network Protocol!".getBytes(),
            false  // 不是最后一帧
        );
        
        byte[] dataFrameData = dataFrame.serialize();
        System.out.println("  ✅ 数据帧: " + dataFrameData.length + " 字节");
        System.out.println("     序列号: " + dataFrame.getSequenceNumber());
        System.out.println("     数据长度: " + dataFrame.getData().length);
        System.out.println("     是否最后帧: " + dataFrame.isLastFrame());
        
        // 3. 确认帧
        AckFrame ackFrame = new AckFrame(1, true);
        byte[] ackData = ackFrame.serialize();
        System.out.println("  ✅ 确认帧: " + ackData.length + " 字节");
        System.out.println("     确认序列号: " + ackFrame.getAckSequenceNumber());
        System.out.println("     确认状态: " + (ackFrame.isSuccess() ? "成功" : "失败"));
        
        // 4. 心跳帧
        HeartbeatFrame heartbeat = new HeartbeatFrame(System.currentTimeMillis());
        byte[] heartbeatData = heartbeat.serialize();
        System.out.println("  ✅ 心跳帧: " + heartbeatData.length + " 字节");
        System.out.println("     时间戳: " + heartbeat.getTimestamp());
        
        System.out.println("\n🔍 测试帧的序列化和反序列化:");
        
        // 测试序列化和反序列化
        HandshakeFrame deserializedHandshake = HandshakeFrame.deserialize(handshakeData);
        assertEquals("握手帧版本应该一致", handshake.getVersion(), deserializedHandshake.getVersion());
        assertArrayEquals("握手帧公钥应该一致", handshake.getPublicKey(), deserializedHandshake.getPublicKey());
        
        DataFrame deserializedData = DataFrame.deserialize(dataFrameData);
        assertEquals("数据帧序列号应该一致", dataFrame.getSequenceNumber(), deserializedData.getSequenceNumber());
        assertArrayEquals("数据帧内容应该一致", dataFrame.getData(), deserializedData.getData());
        
        System.out.println("  ✅ 握手帧序列化/反序列化成功");
        System.out.println("  ✅ 数据帧序列化/反序列化成功");
        
        System.out.println("✅ 协议帧结构测试完成\n");
    }
    
    /**
     * 测试2：连接建立握手过程
     */
    @Test
    public void testConnectionHandshake() {
        System.out.println("🤝 开始连接建立握手测试");
        System.out.println("=" .repeat(50));
        
        // 模拟Alice和Bob的握手过程
        System.out.println("👥 模拟Alice和Bob的握手过程:");
        
        // Alice发起连接
        ProtocolConnection aliceConnection = new ProtocolConnection("Alice", true);
        ProtocolConnection bobConnection = new ProtocolConnection("Bob", false);
        
        System.out.println("  📤 Alice发送握手请求");
        
        // 第一步：Alice发送握手请求
        HandshakeFrame aliceHandshake = aliceConnection.createHandshakeFrame();
        byte[] aliceHandshakeData = aliceHandshake.serialize();
        
        System.out.println("     Alice握手帧大小: " + aliceHandshakeData.length + " 字节");
        System.out.println("     Alice协议版本: " + aliceHandshake.getVersion());
        
        // 第二步：Bob接收并响应握手
        System.out.println("  📥 Bob接收握手请求并响应");
        
        HandshakeFrame receivedHandshake = HandshakeFrame.deserialize(aliceHandshakeData);
        boolean handshakeValid = bobConnection.processHandshakeFrame(receivedHandshake);
        assertTrue("Bob应该接受Alice的握手", handshakeValid);
        
        HandshakeFrame bobHandshake = bobConnection.createHandshakeFrame();
        byte[] bobHandshakeData = bobHandshake.serialize();
        
        System.out.println("     Bob握手帧大小: " + bobHandshakeData.length + " 字节");
        System.out.println("     Bob协议版本: " + bobHandshake.getVersion());
        
        // 第三步：Alice确认Bob的握手
        System.out.println("  ✅ Alice确认Bob的握手");
        
        HandshakeFrame bobReceived = HandshakeFrame.deserialize(bobHandshakeData);
        boolean bobHandshakeValid = aliceConnection.processHandshakeFrame(bobReceived);
        assertTrue("Alice应该接受Bob的握手", bobHandshakeValid);
        
        // 验证连接状态
        assertEquals("Alice连接应该已建立", ConnectionState.ESTABLISHED, aliceConnection.getState());
        assertEquals("Bob连接应该已建立", ConnectionState.ESTABLISHED, bobConnection.getState());
        
        System.out.println("  🎉 握手完成，连接已建立");
        System.out.println("     Alice状态: " + aliceConnection.getState());
        System.out.println("     Bob状态: " + bobConnection.getState());
        
        System.out.println("✅ 连接建立握手测试完成\n");
    }
    
    /**
     * 测试3：可靠数据传输
     */
    @Test
    public void testReliableDataTransmission() {
        System.out.println("📡 开始可靠数据传输测试");
        System.out.println("=" .repeat(50));
        
        // 建立连接
        ProtocolConnection sender = new ProtocolConnection("Sender", true);
        ProtocolConnection receiver = new ProtocolConnection("Receiver", false);
        
        // 模拟握手完成
        sender.setState(ConnectionState.ESTABLISHED);
        receiver.setState(ConnectionState.ESTABLISHED);
        
        System.out.println("🔗 连接已建立，开始数据传输测试");
        
        // 测试数据
        String[] testMessages = {
            "第一条消息：Hello Briar!",
            "第二条消息：这是一个较长的消息，用来测试分片传输功能。",
            "第三条消息：包含特殊字符 @#$%^&*()_+",
            "第四条消息：最后一条测试消息。"
        };
        
        System.out.println("\n📤 发送方传输数据:");
        
        List<DataFrame> sentFrames = new ArrayList<>();
        
        for (int i = 0; i < testMessages.length; i++) {
            String message = testMessages[i];
            DataFrame frame = sender.createDataFrame(i + 1, message.getBytes(), i == testMessages.length - 1);
            sentFrames.add(frame);
            
            System.out.println("  📦 发送帧 " + (i + 1) + ": " + message.substring(0, Math.min(20, message.length())) + "...");
            System.out.println("     序列号: " + frame.getSequenceNumber());
            System.out.println("     数据长度: " + frame.getData().length + " 字节");
        }
        
        System.out.println("\n📥 接收方处理数据:");
        
        List<String> receivedMessages = new ArrayList<>();
        
        for (DataFrame frame : sentFrames) {
            byte[] frameData = frame.serialize();
            DataFrame receivedFrame = DataFrame.deserialize(frameData);
            
            boolean processed = receiver.processDataFrame(receivedFrame);
            assertTrue("数据帧应该被成功处理", processed);
            
            String receivedMessage = new String(receivedFrame.getData());
            receivedMessages.add(receivedMessage);
            
            System.out.println("  📨 接收帧 " + receivedFrame.getSequenceNumber() + ": " + 
                             receivedMessage.substring(0, Math.min(20, receivedMessage.length())) + "...");
            
            // 发送确认
            AckFrame ack = receiver.createAckFrame(receivedFrame.getSequenceNumber(), true);
            byte[] ackData = ack.serialize();
            AckFrame receivedAck = AckFrame.deserialize(ackData);
            sender.processAckFrame(receivedAck);
            
            System.out.println("     ✅ 确认帧已发送和处理");
        }
        
        // 验证传输结果
        System.out.println("\n🔍 验证传输结果:");
        assertEquals("接收的消息数量应该正确", testMessages.length, receivedMessages.size());
        
        for (int i = 0; i < testMessages.length; i++) {
            assertEquals("消息 " + (i + 1) + " 应该完整传输", testMessages[i], receivedMessages.get(i));
            System.out.println("  ✅ 消息 " + (i + 1) + " 传输正确");
        }
        
        System.out.println("✅ 可靠数据传输测试完成\n");
    }
    
    /**
     * 测试4：连接保活机制
     */
    @Test
    public void testConnectionKeepalive() {
        System.out.println("💓 开始连接保活机制测试");
        System.out.println("=" .repeat(50));
        
        // 创建连接
        ProtocolConnection connection = new ProtocolConnection("TestNode", true);
        connection.setState(ConnectionState.ESTABLISHED);
        
        System.out.println("🔗 连接已建立，开始保活测试");
        
        // 模拟心跳发送
        System.out.println("\n💓 发送心跳包:");
        
        List<HeartbeatFrame> heartbeats = new ArrayList<>();
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < 5; i++) {
            long timestamp = startTime + (i * 1000); // 每秒一个心跳
            HeartbeatFrame heartbeat = connection.createHeartbeatFrame(timestamp);
            heartbeats.add(heartbeat);
            
            System.out.println("  💓 心跳 " + (i + 1) + ": 时间戳 " + timestamp);
        }
        
        // 模拟心跳接收和处理
        System.out.println("\n📥 处理接收到的心跳:");
        
        for (int i = 0; i < heartbeats.size(); i++) {
            HeartbeatFrame heartbeat = heartbeats.get(i);
            byte[] heartbeatData = heartbeat.serialize();
            HeartbeatFrame receivedHeartbeat = HeartbeatFrame.deserialize(heartbeatData);
            
            boolean processed = connection.processHeartbeatFrame(receivedHeartbeat);
            assertTrue("心跳应该被成功处理", processed);
            
            System.out.println("  ✅ 心跳 " + (i + 1) + " 处理成功");
            
            // 更新最后活跃时间
            connection.updateLastActiveTime(receivedHeartbeat.getTimestamp());
        }
        
        // 测试连接超时检测
        System.out.println("\n⏰ 测试连接超时检测:");
        
        long currentTime = System.currentTimeMillis();
        long lastActiveTime = connection.getLastActiveTime();
        long idleTime = currentTime - lastActiveTime;
        
        System.out.println("  当前时间: " + currentTime);
        System.out.println("  最后活跃时间: " + lastActiveTime);
        System.out.println("  空闲时间: " + idleTime + " ms");
        
        boolean isAlive = connection.isConnectionAlive(currentTime, 30000); // 30秒超时
        assertTrue("连接应该仍然活跃", isAlive);
        System.out.println("  ✅ 连接状态: " + (isAlive ? "活跃" : "超时"));
        
        // 模拟长时间无心跳
        System.out.println("\n⚠️ 模拟长时间无心跳:");
        long futureTime = currentTime + 60000; // 60秒后
        boolean isStillAlive = connection.isConnectionAlive(futureTime, 30000);
        assertFalse("连接应该已超时", isStillAlive);
        System.out.println("  ⚠️ 连接状态: " + (isStillAlive ? "活跃" : "超时"));
        
        System.out.println("✅ 连接保活机制测试完成\n");
    }
    
    /**
     * 测试5：协议版本兼容性
     */
    @Test
    public void testProtocolVersionCompatibility() {
        System.out.println("🔄 开始协议版本兼容性测试");
        System.out.println("=" .repeat(50));
        
        // 测试不同版本的兼容性
        ProtocolVersion[] versions = {
            ProtocolVersion.V1,
            ProtocolVersion.V2, 
            ProtocolVersion.V3,
            ProtocolVersion.V4
        };
        
        System.out.println("📋 支持的协议版本:");
        for (ProtocolVersion version : versions) {
            System.out.println("  - " + version + " (值: " + version.getValue() + ")");
        }
        
        // 测试版本协商
        System.out.println("\n🤝 测试版本协商:");
        
        ProtocolConnection v4Client = new ProtocolConnection("V4Client", true);
        ProtocolConnection v3Server = new ProtocolConnection("V3Server", false);
        
        // 客户端支持V4，服务器支持V3
        v4Client.setSupportedVersions(Arrays.asList(ProtocolVersion.V4, ProtocolVersion.V3));
        v3Server.setSupportedVersions(Arrays.asList(ProtocolVersion.V3, ProtocolVersion.V2));
        
        System.out.println("  客户端支持: V4, V3");
        System.out.println("  服务器支持: V3, V2");
        
        // 协商最高兼容版本
        ProtocolVersion negotiatedVersion = protocolManager.negotiateVersion(
            v4Client.getSupportedVersions(),
            v3Server.getSupportedVersions()
        );
        
        assertEquals("应该协商到V3版本", ProtocolVersion.V3, negotiatedVersion);
        System.out.println("  ✅ 协商结果: " + negotiatedVersion);
        
        // 测试不兼容的情况
        System.out.println("\n❌ 测试不兼容情况:");
        
        ProtocolConnection v4OnlyClient = new ProtocolConnection("V4OnlyClient", true);
        ProtocolConnection v2OnlyServer = new ProtocolConnection("V2OnlyServer", false);
        
        v4OnlyClient.setSupportedVersions(Arrays.asList(ProtocolVersion.V4));
        v2OnlyServer.setSupportedVersions(Arrays.asList(ProtocolVersion.V2));
        
        System.out.println("  客户端支持: V4");
        System.out.println("  服务器支持: V2");
        
        ProtocolVersion incompatibleResult = protocolManager.negotiateVersion(
            v4OnlyClient.getSupportedVersions(),
            v2OnlyServer.getSupportedVersions()
        );
        
        assertNull("不兼容的版本应该返回null", incompatibleResult);
        System.out.println("  ❌ 协商结果: 无兼容版本");
        
        System.out.println("✅ 协议版本兼容性测试完成\n");
    }

    // ========== 协议相关类定义 ==========

    /**
     * 协议版本枚举
     */
    enum ProtocolVersion {
        V1(1), V2(2), V3(3), V4(4);

        private final int value;

        ProtocolVersion(int value) {
            this.value = value;
        }

        public int getValue() { return value; }

        public static ProtocolVersion fromValue(int value) {
            for (ProtocolVersion version : values()) {
                if (version.value == value) return version;
            }
            throw new IllegalArgumentException("未知协议版本: " + value);
        }
    }

    /**
     * 连接状态枚举
     */
    enum ConnectionState {
        DISCONNECTED,    // 未连接
        CONNECTING,      // 连接中
        HANDSHAKING,     // 握手中
        ESTABLISHED,     // 已建立
        CLOSING,         // 关闭中
        CLOSED           // 已关闭
    }

    /**
     * 帧类型枚举
     */
    enum FrameType {
        HANDSHAKE(1),
        DATA(2),
        ACK(3),
        HEARTBEAT(4);

        private final int value;

        FrameType(int value) {
            this.value = value;
        }

        public int getValue() { return value; }

        public static FrameType fromValue(int value) {
            for (FrameType type : values()) {
                if (type.value == value) return type;
            }
            throw new IllegalArgumentException("未知帧类型: " + value);
        }
    }

    /**
     * 抽象协议帧基类
     */
    abstract static class ProtocolFrame {
        protected final FrameType type;

        public ProtocolFrame(FrameType type) {
            this.type = type;
        }

        public FrameType getType() { return type; }

        public abstract byte[] serialize();

        protected ByteBuffer createBuffer(int capacity) {
            ByteBuffer buffer = ByteBuffer.allocate(capacity);
            buffer.putInt(type.getValue()); // 帧类型
            return buffer;
        }

        protected static FrameType readFrameType(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            int typeValue = buffer.getInt();
            return FrameType.fromValue(typeValue);
        }
    }

    /**
     * 握手帧
     */
    static class HandshakeFrame extends ProtocolFrame {
        private final ProtocolVersion version;
        private final byte[] publicKey;
        private final long timestamp;

        public HandshakeFrame(ProtocolVersion version, byte[] publicKey, long timestamp) {
            super(FrameType.HANDSHAKE);
            this.version = version;
            this.publicKey = publicKey.clone();
            this.timestamp = timestamp;
        }

        public ProtocolVersion getVersion() { return version; }
        public byte[] getPublicKey() { return publicKey.clone(); }
        public long getTimestamp() { return timestamp; }

        @Override
        public byte[] serialize() {
            ByteBuffer buffer = createBuffer(4 + 4 + 4 + publicKey.length + 8);
            buffer.putInt(version.getValue());
            buffer.putInt(publicKey.length);
            buffer.put(publicKey);
            buffer.putLong(timestamp);
            return buffer.array();
        }

        public static HandshakeFrame deserialize(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            FrameType type = FrameType.fromValue(buffer.getInt());
            if (type != FrameType.HANDSHAKE) {
                throw new IllegalArgumentException("不是握手帧");
            }

            ProtocolVersion version = ProtocolVersion.fromValue(buffer.getInt());
            int keyLength = buffer.getInt();
            byte[] publicKey = new byte[keyLength];
            buffer.get(publicKey);
            long timestamp = buffer.getLong();

            return new HandshakeFrame(version, publicKey, timestamp);
        }
    }

    /**
     * 数据帧
     */
    static class DataFrame extends ProtocolFrame {
        private final int sequenceNumber;
        private final byte[] data;
        private final boolean lastFrame;

        public DataFrame(int sequenceNumber, byte[] data, boolean lastFrame) {
            super(FrameType.DATA);
            this.sequenceNumber = sequenceNumber;
            this.data = data.clone();
            this.lastFrame = lastFrame;
        }

        public int getSequenceNumber() { return sequenceNumber; }
        public byte[] getData() { return data.clone(); }
        public boolean isLastFrame() { return lastFrame; }

        @Override
        public byte[] serialize() {
            ByteBuffer buffer = createBuffer(4 + 4 + 4 + data.length + 1);
            buffer.putInt(sequenceNumber);
            buffer.putInt(data.length);
            buffer.put(data);
            buffer.put((byte)(lastFrame ? 1 : 0));
            return buffer.array();
        }

        public static DataFrame deserialize(byte[] frameData) {
            ByteBuffer buffer = ByteBuffer.wrap(frameData);
            FrameType type = FrameType.fromValue(buffer.getInt());
            if (type != FrameType.DATA) {
                throw new IllegalArgumentException("不是数据帧");
            }

            int sequenceNumber = buffer.getInt();
            int dataLength = buffer.getInt();
            byte[] data = new byte[dataLength];
            buffer.get(data);
            boolean lastFrame = buffer.get() == 1;

            return new DataFrame(sequenceNumber, data, lastFrame);
        }
    }

    /**
     * 确认帧
     */
    static class AckFrame extends ProtocolFrame {
        private final int ackSequenceNumber;
        private final boolean success;

        public AckFrame(int ackSequenceNumber, boolean success) {
            super(FrameType.ACK);
            this.ackSequenceNumber = ackSequenceNumber;
            this.success = success;
        }

        public int getAckSequenceNumber() { return ackSequenceNumber; }
        public boolean isSuccess() { return success; }

        @Override
        public byte[] serialize() {
            ByteBuffer buffer = createBuffer(4 + 4 + 1);
            buffer.putInt(ackSequenceNumber);
            buffer.put((byte)(success ? 1 : 0));
            return buffer.array();
        }

        public static AckFrame deserialize(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            FrameType type = FrameType.fromValue(buffer.getInt());
            if (type != FrameType.ACK) {
                throw new IllegalArgumentException("不是确认帧");
            }

            int ackSequenceNumber = buffer.getInt();
            boolean success = buffer.get() == 1;

            return new AckFrame(ackSequenceNumber, success);
        }
    }

    /**
     * 心跳帧
     */
    static class HeartbeatFrame extends ProtocolFrame {
        private final long timestamp;

        public HeartbeatFrame(long timestamp) {
            super(FrameType.HEARTBEAT);
            this.timestamp = timestamp;
        }

        public long getTimestamp() { return timestamp; }

        @Override
        public byte[] serialize() {
            ByteBuffer buffer = createBuffer(4 + 8);
            buffer.putLong(timestamp);
            return buffer.array();
        }

        public static HeartbeatFrame deserialize(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            FrameType type = FrameType.fromValue(buffer.getInt());
            if (type != FrameType.HEARTBEAT) {
                throw new IllegalArgumentException("不是心跳帧");
            }

            long timestamp = buffer.getLong();
            return new HeartbeatFrame(timestamp);
        }
    }

    // ========== 连接和管理器类 ==========

    /**
     * 协议连接
     */
    static class ProtocolConnection {
        private final String nodeId;
        private final boolean isInitiator;
        private ConnectionState state = ConnectionState.DISCONNECTED;
        private ProtocolVersion negotiatedVersion;
        private List<ProtocolVersion> supportedVersions;
        private long lastActiveTime;
        private int nextSequenceNumber = 1;
        private final Map<Integer, DataFrame> sentFrames = new ConcurrentHashMap<>();
        private final Set<Integer> acknowledgedFrames = ConcurrentHashMap.newKeySet();

        public ProtocolConnection(String nodeId, boolean isInitiator) {
            this.nodeId = nodeId;
            this.isInitiator = isInitiator;
            this.supportedVersions = Arrays.asList(ProtocolVersion.values());
            this.lastActiveTime = System.currentTimeMillis();
        }

        // Getters and Setters
        public String getNodeId() { return nodeId; }
        public boolean isInitiator() { return isInitiator; }
        public ConnectionState getState() { return state; }
        public void setState(ConnectionState state) { this.state = state; }
        public ProtocolVersion getNegotiatedVersion() { return negotiatedVersion; }
        public void setNegotiatedVersion(ProtocolVersion version) { this.negotiatedVersion = version; }
        public List<ProtocolVersion> getSupportedVersions() { return supportedVersions; }
        public void setSupportedVersions(List<ProtocolVersion> versions) { this.supportedVersions = versions; }
        public long getLastActiveTime() { return lastActiveTime; }
        public void updateLastActiveTime(long time) { this.lastActiveTime = time; }

        /**
         * 创建握手帧
         */
        public HandshakeFrame createHandshakeFrame() {
            ProtocolVersion version = supportedVersions.get(0); // 使用最高版本
            byte[] publicKey = (nodeId + "_public_key").getBytes();
            long timestamp = System.currentTimeMillis();
            return new HandshakeFrame(version, publicKey, timestamp);
        }

        /**
         * 处理握手帧
         */
        public boolean processHandshakeFrame(HandshakeFrame frame) {
            // 检查版本兼容性
            if (!supportedVersions.contains(frame.getVersion())) {
                return false;
            }

            // 设置协商版本
            negotiatedVersion = frame.getVersion();
            state = ConnectionState.ESTABLISHED;
            updateLastActiveTime(frame.getTimestamp());

            return true;
        }

        /**
         * 创建数据帧
         */
        public DataFrame createDataFrame(int sequenceNumber, byte[] data, boolean lastFrame) {
            DataFrame frame = new DataFrame(sequenceNumber, data, lastFrame);
            sentFrames.put(sequenceNumber, frame);
            return frame;
        }

        /**
         * 处理数据帧
         */
        public boolean processDataFrame(DataFrame frame) {
            if (state != ConnectionState.ESTABLISHED) {
                return false;
            }

            updateLastActiveTime(System.currentTimeMillis());
            return true;
        }

        /**
         * 创建确认帧
         */
        public AckFrame createAckFrame(int sequenceNumber, boolean success) {
            return new AckFrame(sequenceNumber, success);
        }

        /**
         * 处理确认帧
         */
        public boolean processAckFrame(AckFrame frame) {
            if (frame.isSuccess()) {
                acknowledgedFrames.add(frame.getAckSequenceNumber());
                sentFrames.remove(frame.getAckSequenceNumber());
            }
            updateLastActiveTime(System.currentTimeMillis());
            return true;
        }

        /**
         * 创建心跳帧
         */
        public HeartbeatFrame createHeartbeatFrame(long timestamp) {
            return new HeartbeatFrame(timestamp);
        }

        /**
         * 处理心跳帧
         */
        public boolean processHeartbeatFrame(HeartbeatFrame frame) {
            updateLastActiveTime(frame.getTimestamp());
            return true;
        }

        /**
         * 检查连接是否活跃
         */
        public boolean isConnectionAlive(long currentTime, long timeoutMs) {
            return (currentTime - lastActiveTime) <= timeoutMs;
        }

        /**
         * 获取下一个序列号
         */
        public int getNextSequenceNumber() {
            return nextSequenceNumber++;
        }
    }

    /**
     * 协议管理器
     */
    static class ProtocolManager {
        private final Map<String, ProtocolConnection> connections = new ConcurrentHashMap<>();

        /**
         * 协商协议版本
         */
        public ProtocolVersion negotiateVersion(List<ProtocolVersion> clientVersions,
                                               List<ProtocolVersion> serverVersions) {
            // 找到双方都支持的最高版本
            for (ProtocolVersion clientVersion : clientVersions) {
                if (serverVersions.contains(clientVersion)) {
                    return clientVersion;
                }
            }
            return null; // 无兼容版本
        }

        /**
         * 注册连接
         */
        public void registerConnection(ProtocolConnection connection) {
            connections.put(connection.getNodeId(), connection);
        }

        /**
         * 获取连接
         */
        public ProtocolConnection getConnection(String nodeId) {
            return connections.get(nodeId);
        }

        /**
         * 移除连接
         */
        public void removeConnection(String nodeId) {
            connections.remove(nodeId);
        }

        /**
         * 获取所有活跃连接
         */
        public Collection<ProtocolConnection> getActiveConnections() {
            return connections.values().stream()
                    .filter(conn -> conn.getState() == ConnectionState.ESTABLISHED)
                    .collect(Collectors.toList());
        }
    }

    /**
     * 连接管理器
     */
    static class ConnectionManager {
        private final ProtocolManager protocolManager = new ProtocolManager();
        private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);
        private final long heartbeatInterval = 30000; // 30秒
        private final long connectionTimeout = 60000;  // 60秒

        /**
         * 启动连接管理
         */
        public void start() {
            // 定期发送心跳
            scheduler.scheduleAtFixedRate(this::sendHeartbeats,
                    heartbeatInterval, heartbeatInterval, TimeUnit.MILLISECONDS);

            // 定期检查连接超时
            scheduler.scheduleAtFixedRate(this::checkConnectionTimeouts,
                    connectionTimeout, connectionTimeout, TimeUnit.MILLISECONDS);
        }

        /**
         * 停止连接管理
         */
        public void stop() {
            scheduler.shutdown();
        }

        /**
         * 发送心跳
         */
        private void sendHeartbeats() {
            long currentTime = System.currentTimeMillis();
            for (ProtocolConnection connection : protocolManager.getActiveConnections()) {
                HeartbeatFrame heartbeat = connection.createHeartbeatFrame(currentTime);
                // 在实际实现中，这里会通过网络发送心跳
                System.out.println("💓 发送心跳到 " + connection.getNodeId());
            }
        }

        /**
         * 检查连接超时
         */
        private void checkConnectionTimeouts() {
            long currentTime = System.currentTimeMillis();
            List<String> timeoutConnections = new ArrayList<>();

            for (ProtocolConnection connection : protocolManager.getActiveConnections()) {
                if (!connection.isConnectionAlive(currentTime, connectionTimeout)) {
                    timeoutConnections.add(connection.getNodeId());
                }
            }

            for (String nodeId : timeoutConnections) {
                System.out.println("⚠️ 连接超时: " + nodeId);
                protocolManager.removeConnection(nodeId);
            }
        }

        public ProtocolManager getProtocolManager() {
            return protocolManager;
        }
    }
}
