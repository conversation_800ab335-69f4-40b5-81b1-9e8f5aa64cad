package network.learning;

import org.junit.Test;
import org.junit.Before;
import java.util.*;
import java.util.concurrent.*;
import java.io.*;
import java.net.*;
import static org.junit.Assert.*;

/**
 * 传输插件学习实践
 * 
 * 学习目标：
 * 1. 理解Briar的插件化传输架构
 * 2. 掌握不同传输插件的特点和用途
 * 3. 学习连接管理和状态维护
 * 4. 实现简化版的传输插件
 */
public class TransportPluginLearning {
    
    private PluginManager pluginManager;
    private ConnectionRegistry connectionRegistry;
    
    @Before
    public void setUp() {
        pluginManager = new PluginManager();
        connectionRegistry = new ConnectionRegistry();
    }
    
    /**
     * 测试1：插件生命周期管理
     */
    @Test
    public void testPluginLifecycle() {
        System.out.println("🔌 开始插件生命周期测试");
        System.out.println("=" .repeat(50));
        
        // 创建模拟插件
        MockTcpPlugin tcpPlugin = new MockTcpPlugin("TCP");
        MockTorPlugin torPlugin = new MockTorPlugin("TOR");
        
        // 注册插件
        pluginManager.registerPlugin(tcpPlugin);
        pluginManager.registerPlugin(torPlugin);
        
        System.out.println("📋 已注册插件:");
        for (Plugin plugin : pluginManager.getPlugins()) {
            System.out.println("  - " + plugin.getId() + ": " + plugin.getState());
        }
        
        // 启动插件
        System.out.println("\n🚀 启动插件:");
        pluginManager.startAll();
        
        for (Plugin plugin : pluginManager.getPlugins()) {
            assertEquals("插件应该处于活跃状态", PluginState.ACTIVE, plugin.getState());
            System.out.println("  ✅ " + plugin.getId() + ": " + plugin.getState());
        }
        
        // 停止插件
        System.out.println("\n🛑 停止插件:");
        pluginManager.stopAll();
        
        for (Plugin plugin : pluginManager.getPlugins()) {
            assertEquals("插件应该处于非活跃状态", PluginState.INACTIVE, plugin.getState());
            System.out.println("  ✅ " + plugin.getId() + ": " + plugin.getState());
        }
        
        System.out.println("✅ 插件生命周期测试完成\n");
    }
    
    /**
     * 测试2：双工插件连接创建
     */
    @Test
    public void testDuplexPluginConnection() {
        System.out.println("🔗 开始双工插件连接测试");
        System.out.println("=" .repeat(50));
        
        MockTcpPlugin tcpPlugin = new MockTcpPlugin("TCP");
        tcpPlugin.start();
        
        // 创建传输属性
        TransportProperties properties = new TransportProperties();
        properties.put("address", "*************");
        properties.put("port", "8080");
        
        System.out.println("🌐 尝试创建TCP连接:");
        System.out.println("  地址: " + properties.get("address"));
        System.out.println("  端口: " + properties.get("port"));
        
        // 创建连接
        DuplexTransportConnection connection = tcpPlugin.createConnection(properties);
        
        assertNotNull("应该成功创建连接", connection);
        assertTrue("连接应该是双工的", connection instanceof DuplexTransportConnection);
        assertEquals("连接应该关联到正确的插件", tcpPlugin, connection.getPlugin());
        
        System.out.println("✅ TCP连接创建成功");
        System.out.println("  连接ID: " + connection.getConnectionId());
        System.out.println("  插件: " + connection.getPlugin().getId());
        
        // 测试连接功能
        System.out.println("\n📡 测试连接功能:");
        
        try {
            // 模拟发送数据
            String testMessage = "Hello, Briar Network!";
            connection.getOutputStream().write(testMessage.getBytes());
            System.out.println("  ✅ 数据发送成功: " + testMessage);
            
            // 模拟接收数据
            byte[] buffer = new byte[1024];
            int bytesRead = connection.getInputStream().read(buffer);
            String receivedMessage = new String(buffer, 0, bytesRead);
            System.out.println("  ✅ 数据接收成功: " + receivedMessage);
            
            assertEquals("发送和接收的数据应该一致", testMessage, receivedMessage);
            
        } catch (IOException e) {
            fail("连接操作不应该失败: " + e.getMessage());
        } finally {
            connection.close();
            System.out.println("  ✅ 连接已关闭");
        }
        
        System.out.println("✅ 双工插件连接测试完成\n");
    }
    
    /**
     * 测试3：单工插件读写操作
     */
    @Test
    public void testSimplexPluginOperations() {
        System.out.println("📁 开始单工插件操作测试");
        System.out.println("=" .repeat(50));
        
        MockFilePlugin filePlugin = new MockFilePlugin("FILE");
        filePlugin.start();
        
        // 创建传输属性
        TransportProperties writeProps = new TransportProperties();
        writeProps.put("path", "/tmp/briar_test_write.dat");
        
        TransportProperties readProps = new TransportProperties();
        readProps.put("path", "/tmp/briar_test_read.dat");
        
        System.out.println("📝 测试文件写入:");
        
        // 创建写入器
        TransportConnectionWriter writer = filePlugin.createWriter(writeProps);
        assertNotNull("应该成功创建写入器", writer);
        
        try {
            String testData = "Briar file transport test data\nLine 2\nLine 3";
            writer.getOutputStream().write(testData.getBytes());
            writer.close();
            System.out.println("  ✅ 数据写入成功");
            System.out.println("  文件路径: " + writeProps.get("path"));
            System.out.println("  数据长度: " + testData.length() + " 字节");
        } catch (IOException e) {
            fail("文件写入不应该失败: " + e.getMessage());
        }
        
        System.out.println("\n📖 测试文件读取:");
        
        // 创建读取器
        TransportConnectionReader reader = filePlugin.createReader(readProps);
        assertNotNull("应该成功创建读取器", reader);
        
        try {
            byte[] buffer = new byte[1024];
            int bytesRead = reader.getInputStream().read(buffer);
            String readData = new String(buffer, 0, bytesRead);
            reader.close();
            
            System.out.println("  ✅ 数据读取成功");
            System.out.println("  读取长度: " + bytesRead + " 字节");
            System.out.println("  读取内容: " + readData.substring(0, Math.min(50, readData.length())) + "...");
            
        } catch (IOException e) {
            fail("文件读取不应该失败: " + e.getMessage());
        }
        
        System.out.println("✅ 单工插件操作测试完成\n");
    }
    
    /**
     * 测试4：连接注册和管理
     */
    @Test
    public void testConnectionManagement() {
        System.out.println("🔗 开始连接管理测试");
        System.out.println("=" .repeat(50));
        
        MockTcpPlugin tcpPlugin = new MockTcpPlugin("TCP");
        MockTorPlugin torPlugin = new MockTorPlugin("TOR");
        
        tcpPlugin.start();
        torPlugin.start();
        
        // 创建多个连接
        System.out.println("🌐 创建多个连接:");
        
        String contactId1 = "contact_alice";
        String contactId2 = "contact_bob";
        
        // Alice的TCP连接
        TransportProperties tcpProps = new TransportProperties();
        tcpProps.put("address", "*************");
        tcpProps.put("port", "8080");
        DuplexTransportConnection tcpConn = tcpPlugin.createConnection(tcpProps);
        
        // Alice的Tor连接
        TransportProperties torProps = new TransportProperties();
        torProps.put("onion", "3g2upl4pq6kufc4m.onion");
        torProps.put("port", "80");
        DuplexTransportConnection torConn = torPlugin.createConnection(torProps);
        
        // Bob的TCP连接
        TransportProperties tcpProps2 = new TransportProperties();
        tcpProps2.put("address", "*************");
        tcpProps2.put("port", "8080");
        DuplexTransportConnection tcpConn2 = tcpPlugin.createConnection(tcpProps2);
        
        // 注册连接
        connectionRegistry.registerConnection(contactId1, tcpConn);
        connectionRegistry.registerConnection(contactId1, torConn);
        connectionRegistry.registerConnection(contactId2, tcpConn2);
        
        System.out.println("  ✅ Alice (TCP): " + tcpConn.getConnectionId());
        System.out.println("  ✅ Alice (Tor): " + torConn.getConnectionId());
        System.out.println("  ✅ Bob (TCP): " + tcpConn2.getConnectionId());
        
        // 验证连接状态
        System.out.println("\n📊 连接状态统计:");
        
        Set<String> connectedContacts = connectionRegistry.getConnectedContacts();
        assertEquals("应该有2个已连接的联系人", 2, connectedContacts.size());
        assertTrue("Alice应该已连接", connectedContacts.contains(contactId1));
        assertTrue("Bob应该已连接", connectedContacts.contains(contactId2));
        
        System.out.println("  已连接联系人数: " + connectedContacts.size());
        System.out.println("  总连接数: " + connectionRegistry.getTotalConnections());
        
        // 测试连接查询
        Set<DuplexTransportConnection> aliceConnections = 
            connectionRegistry.getConnections(contactId1);
        assertEquals("Alice应该有2个连接", 2, aliceConnections.size());
        
        Set<DuplexTransportConnection> bobConnections = 
            connectionRegistry.getConnections(contactId2);
        assertEquals("Bob应该有1个连接", 1, bobConnections.size());
        
        System.out.println("  Alice的连接数: " + aliceConnections.size());
        System.out.println("  Bob的连接数: " + bobConnections.size());
        
        // 关闭连接
        System.out.println("\n🔌 关闭连接:");
        
        connectionRegistry.unregisterConnection(contactId1, tcpConn);
        tcpConn.close();
        System.out.println("  ✅ Alice的TCP连接已关闭");
        
        connectionRegistry.unregisterConnection(contactId1, torConn);
        torConn.close();
        System.out.println("  ✅ Alice的Tor连接已关闭");
        
        connectionRegistry.unregisterConnection(contactId2, tcpConn2);
        tcpConn2.close();
        System.out.println("  ✅ Bob的TCP连接已关闭");
        
        // 验证清理结果
        assertEquals("所有连接关闭后应该没有已连接的联系人", 
                    0, connectionRegistry.getConnectedContacts().size());
        assertEquals("所有连接关闭后总连接数应该为0", 
                    0, connectionRegistry.getTotalConnections());
        
        System.out.println("✅ 连接管理测试完成\n");
    }
    
    /**
     * 测试5：传输优先级和选择策略
     */
    @Test
    public void testTransportPriorityAndSelection() {
        System.out.println("🎯 开始传输优先级和选择测试");
        System.out.println("=" .repeat(50));
        
        // 创建不同类型的插件
        MockTcpPlugin tcpPlugin = new MockTcpPlugin("TCP");
        MockTorPlugin torPlugin = new MockTorPlugin("TOR");
        MockBluetoothPlugin btPlugin = new MockBluetoothPlugin("BLUETOOTH");
        
        tcpPlugin.start();
        torPlugin.start();
        btPlugin.start();
        
        pluginManager.registerPlugin(tcpPlugin);
        pluginManager.registerPlugin(torPlugin);
        pluginManager.registerPlugin(btPlugin);
        
        // 设置传输优先级
        TransportSelector selector = new TransportSelector();
        selector.setPreference("TOR", 1);      // 最高优先级（匿名性）
        selector.setPreference("TCP", 2);      // 中等优先级（性能）
        selector.setPreference("BLUETOOTH", 3); // 最低优先级（近距离）
        
        System.out.println("🏆 传输优先级设置:");
        System.out.println("  1. TOR (匿名性优先)");
        System.out.println("  2. TCP (性能优先)");
        System.out.println("  3. BLUETOOTH (近距离)");
        
        // 模拟不同场景的传输选择
        System.out.println("\n🎭 场景1: 所有传输都可用");
        List<Plugin> availablePlugins = Arrays.asList(tcpPlugin, torPlugin, btPlugin);
        Plugin selected = selector.selectBestTransport(availablePlugins);
        assertEquals("应该选择Tor（最高优先级）", torPlugin, selected);
        System.out.println("  ✅ 选择结果: " + selected.getId());
        
        System.out.println("\n🎭 场景2: Tor不可用");
        torPlugin.setState(PluginState.DISABLED);
        availablePlugins = Arrays.asList(tcpPlugin, btPlugin);
        selected = selector.selectBestTransport(availablePlugins);
        assertEquals("应该选择TCP（次高优先级）", tcpPlugin, selected);
        System.out.println("  ✅ 选择结果: " + selected.getId());
        
        System.out.println("\n🎭 场景3: 只有蓝牙可用");
        tcpPlugin.setState(PluginState.DISABLED);
        availablePlugins = Arrays.asList(btPlugin);
        selected = selector.selectBestTransport(availablePlugins);
        assertEquals("应该选择蓝牙（唯一可用）", btPlugin, selected);
        System.out.println("  ✅ 选择结果: " + selected.getId());
        
        System.out.println("\n🎭 场景4: 无可用传输");
        btPlugin.setState(PluginState.DISABLED);
        availablePlugins = Arrays.asList();
        selected = selector.selectBestTransport(availablePlugins);
        assertNull("没有可用传输时应该返回null", selected);
        System.out.println("  ✅ 选择结果: 无可用传输");
        
        System.out.println("✅ 传输优先级和选择测试完成\n");
    }
    
    // ========== 辅助类和接口 ==========
    
    /**
     * 插件状态枚举
     */
    enum PluginState {
        INACTIVE, STARTING, ACTIVE, DISABLED
    }
    
    /**
     * 基础插件接口
     */
    interface Plugin {
        String getId();
        PluginState getState();
        void setState(PluginState state);
        void start();
        void stop();
        long getMaxLatency();
        int getMaxIdleTime();
    }
    
    /**
     * 双工插件接口
     */
    interface DuplexPlugin extends Plugin {
        DuplexTransportConnection createConnection(TransportProperties properties);
        boolean supportsKeyAgreement();
    }
    
    /**
     * 单工插件接口
     */
    interface SimplexPlugin extends Plugin {
        boolean isLossyAndCheap();
        TransportConnectionReader createReader(TransportProperties properties);
        TransportConnectionWriter createWriter(TransportProperties properties);
    }
    
    /**
     * 传输属性
     */
    static class TransportProperties extends HashMap<String, String> {
        // 继承HashMap的所有功能
    }
    
    /**
     * 双工传输连接
     */
    static class DuplexTransportConnection {
        private final Plugin plugin;
        private final String connectionId;
        private final ByteArrayInputStream inputStream;
        private final ByteArrayOutputStream outputStream;
        private boolean closed = false;
        
        public DuplexTransportConnection(Plugin plugin) {
            this.plugin = plugin;
            this.connectionId = "conn_" + System.currentTimeMillis() + "_" + 
                               (int)(Math.random() * 1000);
            this.outputStream = new ByteArrayOutputStream();
            this.inputStream = new ByteArrayInputStream(new byte[0]);
        }
        
        public Plugin getPlugin() { return plugin; }
        public String getConnectionId() { return connectionId; }
        
        public InputStream getInputStream() {
            // 模拟：返回之前写入的数据
            byte[] data = outputStream.toByteArray();
            return new ByteArrayInputStream(data);
        }
        
        public OutputStream getOutputStream() { return outputStream; }
        
        public void close() {
            closed = true;
            try {
                outputStream.close();
            } catch (IOException e) {
                // 忽略关闭异常
            }
        }
        
        public boolean isClosed() { return closed; }
    }
    
    /**
     * 传输连接读取器
     */
    static class TransportConnectionReader {
        private final InputStream inputStream;
        
        public TransportConnectionReader(InputStream inputStream) {
            this.inputStream = inputStream;
        }
        
        public InputStream getInputStream() { return inputStream; }
        
        public void close() {
            try {
                inputStream.close();
            } catch (IOException e) {
                // 忽略关闭异常
            }
        }
    }
    
    /**
     * 传输连接写入器
     */
    static class TransportConnectionWriter {
        private final OutputStream outputStream;
        
        public TransportConnectionWriter(OutputStream outputStream) {
            this.outputStream = outputStream;
        }
        
        public OutputStream getOutputStream() { return outputStream; }
        
        public void close() {
            try {
                outputStream.close();
            } catch (IOException e) {
                // 忽略关闭异常
            }
        }
    }

    // ========== 模拟插件实现 ==========

    /**
     * 抽象插件基类
     */
    abstract static class AbstractPlugin implements Plugin {
        protected final String id;
        protected PluginState state = PluginState.INACTIVE;

        public AbstractPlugin(String id) {
            this.id = id;
        }

        @Override
        public String getId() { return id; }

        @Override
        public PluginState getState() { return state; }

        @Override
        public void setState(PluginState state) { this.state = state; }

        @Override
        public void start() {
            state = PluginState.STARTING;
            // 模拟启动延迟
            try { Thread.sleep(10); } catch (InterruptedException e) {}
            state = PluginState.ACTIVE;
        }

        @Override
        public void stop() {
            state = PluginState.INACTIVE;
        }

        @Override
        public long getMaxLatency() { return 30000; } // 30秒

        @Override
        public int getMaxIdleTime() { return 300000; } // 5分钟
    }

    /**
     * 模拟TCP插件
     */
    static class MockTcpPlugin extends AbstractPlugin implements DuplexPlugin {
        public MockTcpPlugin(String id) { super(id); }

        @Override
        public DuplexTransportConnection createConnection(TransportProperties properties) {
            if (state != PluginState.ACTIVE) return null;

            String address = properties.get("address");
            String port = properties.get("port");

            if (address != null && port != null) {
                return new DuplexTransportConnection(this);
            }
            return null;
        }

        @Override
        public boolean supportsKeyAgreement() { return false; }

        @Override
        public long getMaxLatency() { return 1000; } // TCP低延迟
    }

    /**
     * 模拟Tor插件
     */
    static class MockTorPlugin extends AbstractPlugin implements DuplexPlugin {
        public MockTorPlugin(String id) { super(id); }

        @Override
        public DuplexTransportConnection createConnection(TransportProperties properties) {
            if (state != PluginState.ACTIVE) return null;

            String onion = properties.get("onion");
            if (onion != null && onion.endsWith(".onion")) {
                return new DuplexTransportConnection(this);
            }
            return null;
        }

        @Override
        public boolean supportsKeyAgreement() { return false; }

        @Override
        public long getMaxLatency() { return 10000; } // Tor高延迟
    }

    /**
     * 模拟蓝牙插件
     */
    static class MockBluetoothPlugin extends AbstractPlugin implements DuplexPlugin {
        public MockBluetoothPlugin(String id) { super(id); }

        @Override
        public DuplexTransportConnection createConnection(TransportProperties properties) {
            if (state != PluginState.ACTIVE) return null;

            String address = properties.get("address");
            String uuid = properties.get("uuid");

            if (address != null && uuid != null) {
                return new DuplexTransportConnection(this);
            }
            return null;
        }

        @Override
        public boolean supportsKeyAgreement() { return true; }

        @Override
        public long getMaxLatency() { return 5000; } // 蓝牙中等延迟
    }

    /**
     * 模拟文件插件
     */
    static class MockFilePlugin extends AbstractPlugin implements SimplexPlugin {
        public MockFilePlugin(String id) { super(id); }

        @Override
        public boolean isLossyAndCheap() { return false; }

        @Override
        public TransportConnectionReader createReader(TransportProperties properties) {
            if (state != PluginState.ACTIVE) return null;

            String path = properties.get("path");
            if (path != null) {
                // 模拟文件读取
                String mockData = "Mock file data from " + path + "\nLine 2\nLine 3";
                ByteArrayInputStream inputStream = new ByteArrayInputStream(mockData.getBytes());
                return new TransportConnectionReader(inputStream);
            }
            return null;
        }

        @Override
        public TransportConnectionWriter createWriter(TransportProperties properties) {
            if (state != PluginState.ACTIVE) return null;

            String path = properties.get("path");
            if (path != null) {
                // 模拟文件写入
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                return new TransportConnectionWriter(outputStream);
            }
            return null;
        }

        @Override
        public long getMaxLatency() { return 60000; } // 文件传输高延迟
    }

    // ========== 管理器类 ==========

    /**
     * 插件管理器
     */
    static class PluginManager {
        private final List<Plugin> plugins = new ArrayList<>();

        public void registerPlugin(Plugin plugin) {
            plugins.add(plugin);
        }

        public List<Plugin> getPlugins() {
            return new ArrayList<>(plugins);
        }

        public void startAll() {
            for (Plugin plugin : plugins) {
                plugin.start();
            }
        }

        public void stopAll() {
            for (Plugin plugin : plugins) {
                plugin.stop();
            }
        }

        public Plugin getPlugin(String id) {
            return plugins.stream()
                    .filter(p -> p.getId().equals(id))
                    .findFirst()
                    .orElse(null);
        }
    }

    /**
     * 连接注册表
     */
    static class ConnectionRegistry {
        private final Map<String, Set<DuplexTransportConnection>> connections =
                new ConcurrentHashMap<>();

        public void registerConnection(String contactId, DuplexTransportConnection connection) {
            connections.computeIfAbsent(contactId, k -> ConcurrentHashMap.newKeySet())
                      .add(connection);
        }

        public void unregisterConnection(String contactId, DuplexTransportConnection connection) {
            Set<DuplexTransportConnection> contactConnections = connections.get(contactId);
            if (contactConnections != null) {
                contactConnections.remove(connection);
                if (contactConnections.isEmpty()) {
                    connections.remove(contactId);
                }
            }
        }

        public Set<String> getConnectedContacts() {
            return new HashSet<>(connections.keySet());
        }

        public Set<DuplexTransportConnection> getConnections(String contactId) {
            return new HashSet<>(connections.getOrDefault(contactId, Collections.emptySet()));
        }

        public int getTotalConnections() {
            return connections.values().stream()
                    .mapToInt(Set::size)
                    .sum();
        }
    }

    /**
     * 传输选择器
     */
    static class TransportSelector {
        private final Map<String, Integer> preferences = new HashMap<>();

        public void setPreference(String transportId, int priority) {
            preferences.put(transportId, priority);
        }

        public Plugin selectBestTransport(List<Plugin> availablePlugins) {
            return availablePlugins.stream()
                    .filter(p -> p.getState() == PluginState.ACTIVE)
                    .min(Comparator.comparing(p -> preferences.getOrDefault(p.getId(), Integer.MAX_VALUE)))
                    .orElse(null);
        }
    }
}
