<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="database.learning.DatabaseMigrationLearning" time="0.211" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/database-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/org/hsqldb/hsqldb/2.7.1/hsqldb-2.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/surefire/surefirebooter-20250704174302934_3.jar /Volumes/ExtendData/Code/github/briar/database-learning/target/surefire 2025-07-04T17-43-02_836-jvmRun1 surefire-20250704174302934_1tmp surefire_0-20250704174302934_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/database-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/com/h2database/h2/2.1.214/h2-2.1.214.jar:/Users/<USER>/.m2/repository/org/hsqldb/hsqldb/2.7.1/hsqldb-2.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/database-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/database-learning/target/surefire/surefirebooter-20250704174302934_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/database-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testDatabaseMigration" classname="database.learning.DatabaseMigrationLearning" time="0.034">
    <system-out><![CDATA[🔄 开始数据库迁移流程测试
==================================================
📊 创建版本1数据库:
  版本1数据统计:
    用户数: 5
    消息数: 10

🔄 迁移到版本2:
  ✅ 迁移成功:
    从版本: 1
    到版本: 2
    迁移时间: 5ms
  📋 版本2表结构:
    - CONSTANTS
    - ENUM_VALUES
    - INDEXES
    - INDEX_COLUMNS
    - IN_DOUBT
    - LOCKS
    - QUERY_STATISTICS
    - RIGHTS
    - ROLES
    - SESSIONS
    - SESSION_STATE
    - SETTINGS
    - SYNONYMS
    - USERS
    - GROUPS
    - MESSAGES
    - SETTINGS
    - USERS
  📊 数据完整性验证:
    用户数: 5 (保持不变)
    消息数: 10 (保持不变)

🔄 迁移到版本3:
  ✅ 迁移到版本3成功
    迁移时间: 2ms
✅ 数据库迁移流程测试完成

]]></system-out>
  </testcase>
  <testcase name="testMigrationFailureAndRollback" classname="database.learning.DatabaseMigrationLearning" time="0.008">
    <system-out><![CDATA[❌ 开始迁移失败和回滚测试
==================================================
📊 迁移前状态:
  版本: 1
  用户数: 5

💥 尝试失败的迁移:
  ❌ 迁移失败 (预期):
    错误: 不支持的目标版本: 999
  ✅ 回滚验证:
    版本: 1 (未改变)
    用户数: 5 (未改变)
✅ 迁移失败和回滚测试完成

]]></system-out>
  </testcase>
  <testcase name="testDatabaseVersionManagement" classname="database.learning.DatabaseMigrationLearning" time="0.013">
    <system-out><![CDATA[📊 开始数据库版本管理测试
==================================================
🆕 初始化数据库版本1:
  ✅ 当前数据库版本: 1
  📋 版本1表结构:
    - CONSTANTS
    - ENUM_VALUES
    - INDEXES
    - INDEX_COLUMNS
    - IN_DOUBT
    - LOCKS
    - QUERY_STATISTICS
    - RIGHTS
    - ROLES
    - SESSIONS
    - SESSION_STATE
    - SETTINGS
    - SYNONYMS
    - USERS
    - MESSAGES
    - SETTINGS
    - USERS
  📊 测试数据统计:
    用户数: 5
    消息数: 10
✅ 数据库版本管理测试完成

]]></system-out>
  </testcase>
  <testcase name="testLargeDataMigrationPerformance" classname="database.learning.DatabaseMigrationLearning" time="0.006">
    <system-out><![CDATA[⚡ 开始大数据量迁移性能测试
==================================================
📊 创建大数据量数据库:
  📊 大数据量统计:
    用户数: 10
    消息数: 100
    插入时间: 1ms

🔄 执行大数据量迁移:
  ✅ 迁移性能统计:
    迁移时间: 2ms
    数据量: 110 条记录
    平均速度: 55000 记录/秒
  ✅ 数据完整性验证通过
  ✅ 迁移性能良好
✅ 大数据量迁移性能测试完成

]]></system-out>
  </testcase>
  <testcase name="testConcurrentMigrationSafety" classname="database.learning.DatabaseMigrationLearning" time="0.15">
    <system-out><![CDATA[🔒 开始并发迁移安全性测试
==================================================
🔄 模拟并发迁移:
  任务 1 完成: 失败
  任务 0 完成: 成功
  任务 2 完成: 成功

📊 并发迁移结果分析:
    失败原因: Table "MESSAGES" not found; SQL statement:
DROP TABLE "PUBLIC"."MESSAGES" IGNORE [42102-214]
  成功任务数: 2
  失败任务数: 1
  异常数: 0
  ✅ 并发安全性验证通过:
    - 成功迁移数: 2
    - 失败迁移数: 1
    - 锁机制正常工作
✅ 并发迁移安全性测试完成

]]></system-out>
  </testcase>
</testsuite>