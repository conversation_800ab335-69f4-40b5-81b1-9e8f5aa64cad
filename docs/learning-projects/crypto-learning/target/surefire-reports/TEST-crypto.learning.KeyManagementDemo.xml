<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="crypto.learning.KeyManagementDemo" time="0.092" tests="4" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/crypto-learning/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/net/i2p/crypto/eddsa/0.3.0/eddsa-0.3.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire/surefirebooter-20250703154044297_3.jar /Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire 2025-07-03T15-40-44_229-jvmRun1 surefire-20250703154044297_1tmp surefire_0-20250703154044297_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="KeyManagementDemo"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/crypto-learning/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/net/i2p/crypto/eddsa/0.3.0/eddsa-0.3.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/crypto-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire/surefirebooter-20250703154044297_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/crypto-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="demonstrateContextBoundKeyDerivation" classname="crypto.learning.KeyManagementDemo" time="0.072">
    <system-out><![CDATA[🎯 演示上下文绑定的密钥派生
==================================================
🏷️ 为不同上下文派生密钥:
  ALICE_TO_BOB_CHAT: f5d44065d3abc167...
  BOB_TO_ALICE_CHAT: 720a251e050beb19...
  GROUP_CHAT_123: 8308527f0f0fe6c6...
  FILE_TRANSFER_456: 5d67deed6e149a9f...
  VOICE_CALL_789: db2638b620eed379...

🔒 验证上下文隔离:
✅ 所有上下文密钥都是唯一的
✅ 双向通信密钥不对称性验证完成

]]></system-out>
  </testcase>
  <testcase name="demonstrateHierarchicalKeys" classname="crypto.learning.KeyManagementDemo" time="0.001">
    <system-out><![CDATA[🏗️ 演示分层密钥结构
==================================================
🌳 根密钥: 8c82043c5488929b...

📱 第一层 - 协议级密钥:
  聊天协议: 9657b305792a219c...
  文件协议: e165f96ce43df9a9...
  语音协议: e291023de8e056d6...

💬 第二层 - 聊天功能密钥:
  消息加密: 68dff3eb2481956a...
  消息认证: 0a24b92cfa8e1d01...
  元数据保护: 0403b1c974ccd44f...

🔄 第三层 - 会话级密钥:
  会话1: 12d5d5706ffa432f...
  会话2: 6519e093917435e6...
  会话3: f845d2145a93b746...
✅ 分层密钥结构验证完成

]]></system-out>
  </testcase>
  <testcase name="demonstrateBasicKeyDerivation" classname="crypto.learning.KeyManagementDemo" time="0.001">
    <system-out><![CDATA[🔑 演示基本密钥协商和派生过程
==================================================
👥 第一步：模拟Alice和Bob已完成密钥协商
✅ 获得共享密钥
   共享密钥长度: 32 字节
   共享密钥前8字节: 45e198ebb9cfd658...

🔄 第二步：派生专用密钥
✅ 加密密钥: d52e8d37e15393d9...
✅ MAC密钥: ae444522c15d9eb4...
✅ 元数据密钥: 8ec22cd0d9e39c8f...
✅ 所有派生密钥都不相同

🎯 第三步：验证密钥派生的确定性
✅ 密钥派生具有确定性
✅ 基本密钥管理演示完成

]]></system-out>
  </testcase>
  <testcase name="demonstrateKeyRotationAndForwardSecrecy" classname="crypto.learning.KeyManagementDemo" time="0.004">
    <system-out><![CDATA[🔄 演示密钥轮换和前向安全
==================================================
🔑 主密钥: 20c71803fea6ad60...
📅 时期1密钥: fe0ce1809ae84656...
📅 时期2密钥: c7cb03a891e338a3...
📅 时期3密钥: 85f4a559cd40852d...
📅 时期4密钥: a6343266f4a76153...
📅 时期5密钥: 624f2a3afeea3d05...

📝 加密不同时期的消息:
  时期1: 时期1的秘密消息 -> 已加密
  时期2: 时期2的秘密消息 -> 已加密
  时期3: 时期3的秘密消息 -> 已加密
  时期4: 时期4的秘密消息 -> 已加密
  时期5: 时期5的秘密消息 -> 已加密

🚨 模拟安全事件：时期3的密钥被泄露
⚠️ 时期3消息被解密（时期3的秘密消息）
✅ 时期1消息保持安全
✅ 时期2消息保持安全
✅ 时期4消息保持安全
✅ 时期5消息保持安全
✅ 前向安全性验证完成：密钥泄露只影响对应时期

]]></system-out>
  </testcase>
</testsuite>