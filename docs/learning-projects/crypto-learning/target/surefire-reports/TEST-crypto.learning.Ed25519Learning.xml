<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="crypto.learning.Ed25519Learning" time="0.611" tests="6" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/crypto-learning/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/net/i2p/crypto/eddsa/0.3.0/eddsa-0.3.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire/surefirebooter-20250703142959383_3.jar /Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire 2025-07-03T14-29-59_264-jvmRun1 surefire-20250703142959383_1tmp surefire_0-20250703142959383_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/crypto-learning/target/classes:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.70/bcprov-jdk15on-1.70.jar:/Users/<USER>/.m2/repository/net/i2p/crypto/eddsa/0.3.0/eddsa-0.3.0.jar:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/crypto-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/crypto-learning/target/surefire/surefirebooter-20250703142959383_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/crypto-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testPerformance" classname="crypto.learning.Ed25519Learning" time="0.605">
    <system-out><![CDATA[⚡ 开始Ed25519性能测试
📊 性能统计 (1000 次操作):
  签名平均时间: 0.32 ms
  验证平均时间: 0.28 ms
✅ Ed25519性能测试完成

]]></system-out>
  </testcase>
  <testcase name="testDifferentKeyPairs" classname="crypto.learning.Ed25519Learning" time="0.002">
    <system-out><![CDATA[🔐 开始不同密钥对签名测试
✅ 不同密钥对正确被拒绝
✅ 正确密钥对验证成功
✅ 不同密钥对签名测试通过

]]></system-out>
  </testcase>
  <testcase name="testKeyGeneration" classname="crypto.learning.Ed25519Learning" time="0">
    <system-out><![CDATA[🔑 开始Ed25519密钥生成测试
✅ 私钥编码长度: 48 字节
✅ 公钥编码长度: 44 字节
✅ Ed25519密钥生成测试通过

]]></system-out>
  </testcase>
  <testcase name="testDeterministicSignature" classname="crypto.learning.Ed25519Learning" time="0.002">
    <system-out><![CDATA[🎯 开始确定性签名测试
✅ Ed25519确定性签名验证通过
✅ 确定性签名测试通过

]]></system-out>
  </testcase>
  <testcase name="testInvalidMessageVerification" classname="crypto.learning.Ed25519Learning" time="0">
    <system-out><![CDATA[❌ 开始错误消息签名验证测试
✅ 错误消息正确被拒绝
✅ 篡改的签名正确被拒绝
✅ 错误消息签名验证测试通过

]]></system-out>
  </testcase>
  <testcase name="testBasicSignatureVerification" classname="crypto.learning.Ed25519Learning" time="0.001">
    <system-out><![CDATA[✍️ 开始Ed25519基本签名验证测试
📝 消息: Briar secure messaging with Ed25519
✍️ 签名长度: 64 字节
✅ 签名验证成功
✅ Ed25519基本签名验证测试通过

]]></system-out>
  </testcase>
</testsuite>