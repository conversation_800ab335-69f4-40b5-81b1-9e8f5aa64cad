package crypto.learning;

import net.i2p.crypto.eddsa.*;
import net.i2p.crypto.eddsa.spec.*;
import org.bouncycastle.crypto.digests.Blake2bDigest;
import org.junit.Test;
import java.security.*;
import java.security.spec.NamedParameterSpec;
import java.security.KeyPairGenerator;
import static org.junit.Assert.*;

/**
 * 密码学综合集成测试
 * 
 * 整合XSalsa20Poly1305、Ed25519和Blake2b，
 * 演示完整的加密通信流程
 */
public class CryptoIntegrationTest {
    
    private static final EdDSANamedCurveSpec CURVE_SPEC = 
            EdDSANamedCurveTable.getByName("Ed25519");
    
    /**
     * 综合测试：模拟Briar的完整加密通信流程
     */
    @Test
    public void testCompleteEncryptionFlow() throws Exception {
        System.out.println("🚀 开始完整加密通信流程测试");
        System.out.println("=" .repeat(50));
        
        // === 第一步：身份生成 ===
        System.out.println("👤 第一步：生成Alice和Bob的身份");
        
        // Alice生成身份
        KeyPair aliceSigningKeys = generateSigningKeyPair();
        byte[] aliceAgreementKeys = generateAgreementKeyPair();
        System.out.println("✅ Alice身份生成完成");

        // Bob生成身份
        KeyPair bobSigningKeys = generateSigningKeyPair();
        byte[] bobAgreementKeys = generateAgreementKeyPair();
        System.out.println("✅ Bob身份生成完成");
        
        // === 第二步：密钥协商 ===
        System.out.println("\n🤝 第二步：密钥协商");
        
        // 模拟ECDH密钥协商（简化版）
        byte[] sharedSecret = performKeyAgreement(
            aliceAgreementKeys,
            bobAgreementKeys
        );
        
        // 使用Blake2b派生通信密钥
        byte[] communicationKey = deriveKey("COMMUNICATION_KEY", sharedSecret);
        byte[] macKey = deriveKey("MAC_KEY", sharedSecret);
        
        System.out.println("✅ 共享密钥长度: " + sharedSecret.length + " 字节");
        System.out.println("✅ 通信密钥长度: " + communicationKey.length + " 字节");
        System.out.println("✅ MAC密钥长度: " + macKey.length + " 字节");
        
        // === 第三步：消息加密和签名 ===
        System.out.println("\n📝 第三步：Alice发送加密消息给Bob");
        
        String originalMessage = "Hello Bob! This is a secure message from Alice.";
        System.out.println("原始消息: " + originalMessage);
        
        // Alice加密消息
        XSalsa20Poly1305Learning crypto = new XSalsa20Poly1305Learning();
        byte[] iv = generateRandomBytes(24);
        byte[] encryptedMessage = crypto.encrypt(originalMessage.getBytes(), communicationKey, iv);
        
        // Alice对加密消息签名
        byte[] messageSignature = signMessage(encryptedMessage, aliceSigningKeys.getPrivate());
        
        System.out.println("✅ 消息加密完成，密文长度: " + encryptedMessage.length + " 字节");
        System.out.println("✅ 消息签名完成，签名长度: " + messageSignature.length + " 字节");
        
        // === 第四步：消息传输和验证 ===
        System.out.println("\n📨 第四步：Bob接收和验证消息");
        
        // Bob验证Alice的签名
        boolean signatureValid = verifySignature(
            encryptedMessage, 
            messageSignature, 
            aliceSigningKeys.getPublic()
        );
        assertTrue("签名验证应该成功", signatureValid);
        System.out.println("✅ Alice的签名验证成功");
        
        // Bob解密消息
        byte[] decryptedBytes = crypto.decrypt(encryptedMessage, communicationKey, iv);
        String decryptedMessage = new String(decryptedBytes);
        
        assertEquals("解密后的消息应该与原始消息相同", originalMessage, decryptedMessage);
        System.out.println("✅ 消息解密成功: " + decryptedMessage);
        
        // === 第五步：完整性验证 ===
        System.out.println("\n🔍 第五步：完整性验证");
        
        // 计算整个通信会话的完整性哈希
        byte[] sessionHash = calculateSessionHash(
            aliceSigningKeys.getPublic().getEncoded(),
            bobSigningKeys.getPublic().getEncoded(),
            encryptedMessage,
            messageSignature
        );
        
        System.out.println("✅ 会话完整性哈希: " + bytesToHex(sessionHash, 16));
        
        System.out.println("\n🎉 完整加密通信流程测试成功！");
        System.out.println("=" .repeat(50));
    }
    
    /**
     * 测试攻击场景：中间人攻击检测
     */
    @Test
    public void testManInTheMiddleDetection() throws Exception {
        System.out.println("🕵️ 开始中间人攻击检测测试");
        
        // 正常的Alice和Bob
        KeyPair aliceKeys = generateSigningKeyPair();
        KeyPair bobKeys = generateSigningKeyPair();
        
        // 恶意的Mallory
        KeyPair malloryKeys = generateSigningKeyPair();
        
        String message = "Secret message";
        byte[] messageBytes = message.getBytes();
        
        // Alice用自己的密钥签名
        byte[] aliceSignature = signMessage(messageBytes, aliceKeys.getPrivate());
        
        // Mallory尝试伪造Alice的签名
        byte[] mallorySignature = signMessage(messageBytes, malloryKeys.getPrivate());
        
        // Bob验证签名
        boolean aliceSignatureValid = verifySignature(messageBytes, aliceSignature, aliceKeys.getPublic());
        boolean mallorySignatureValid = verifySignature(messageBytes, mallorySignature, aliceKeys.getPublic());
        
        assertTrue("Alice的真实签名应该验证成功", aliceSignatureValid);
        assertFalse("Mallory的伪造签名应该验证失败", mallorySignatureValid);
        
        System.out.println("✅ 成功检测到中间人攻击");
        System.out.println("✅ 数字签名有效防止了身份伪造\n");
    }
    
    /**
     * 测试前向安全性
     */
    @Test
    public void testForwardSecrecy() throws Exception {
        System.out.println("🔄 开始前向安全性测试");
        
        XSalsa20Poly1305Learning crypto = new XSalsa20Poly1305Learning();
        
        // 模拟多个会话，每个会话使用不同的临时密钥
        String[] messages = {
            "Session 1 message",
            "Session 2 message", 
            "Session 3 message"
        };
        
        byte[][] sessionKeys = new byte[3][];
        byte[][] encryptedMessages = new byte[3][];
        byte[][] ivs = new byte[3][];
        
        // 每个会话生成新的临时密钥
        for (int i = 0; i < 3; i++) {
            sessionKeys[i] = generateRandomBytes(32);
            ivs[i] = generateRandomBytes(24);
            encryptedMessages[i] = crypto.encrypt(messages[i].getBytes(), sessionKeys[i], ivs[i]);
            System.out.println("✅ 会话 " + (i+1) + " 加密完成");
        }
        
        // 模拟第2个会话的密钥泄露
        System.out.println("🚨 模拟会话2的密钥泄露");
        
        // 攻击者只能解密会话2的消息
        String decrypted2 = new String(crypto.decrypt(encryptedMessages[1], sessionKeys[1], ivs[1]));
        assertEquals("应该能解密会话2", messages[1], decrypted2);
        System.out.println("⚠️ 会话2被解密: " + decrypted2);
        
        // 但无法解密其他会话（因为密钥不同）
        try {
            crypto.decrypt(encryptedMessages[0], sessionKeys[1], ivs[0]);
            fail("不应该能用会话2的密钥解密会话1");
        } catch (Exception e) {
            System.out.println("✅ 会话1保持安全");
        }
        
        try {
            crypto.decrypt(encryptedMessages[2], sessionKeys[1], ivs[2]);
            fail("不应该能用会话2的密钥解密会话3");
        } catch (Exception e) {
            System.out.println("✅ 会话3保持安全");
        }
        
        System.out.println("✅ 前向安全性验证成功\n");
    }
    
    // ========== 辅助方法 ==========
    
    private KeyPair generateSigningKeyPair() throws Exception {
        KeyPairGenerator keyGen = KeyPairGenerator.getInstance("EdDSA");
        keyGen.initialize(new NamedParameterSpec("Ed25519"));
        return keyGen.generateKeyPair();
    }
    
    private byte[] generateAgreementKeyPair() {
        // 简化：实际应该使用Curve25519
        return generateRandomBytes(32);
    }
    
    private byte[] performKeyAgreement(byte[] aliceKey, byte[] bobKey) {
        // 简化的密钥协商：实际应该使用ECDH
        // 这里只是演示概念
        byte[] combined = new byte[64];
        System.arraycopy(aliceKey, 0, combined, 0, 32);
        System.arraycopy(bobKey, 0, combined, 32, 32);
        return blake2bHash(combined, 32);
    }
    
    private byte[] deriveKey(String label, byte[] secret) {
        byte[] labelBytes = label.getBytes();
        byte[] combined = new byte[labelBytes.length + secret.length];
        System.arraycopy(labelBytes, 0, combined, 0, labelBytes.length);
        System.arraycopy(secret, 0, combined, labelBytes.length, secret.length);
        return blake2bHash(combined, 32);
    }
    
    private byte[] signMessage(byte[] message, PrivateKey privateKey) throws Exception {
        Signature signature = Signature.getInstance("EdDSA");
        signature.initSign(privateKey);
        signature.update(message);
        return signature.sign();
    }
    
    private boolean verifySignature(byte[] message, byte[] signature, PublicKey publicKey) {
        try {
            Signature verifier = Signature.getInstance("EdDSA");
            verifier.initVerify(publicKey);
            verifier.update(message);
            return verifier.verify(signature);
        } catch (Exception e) {
            return false;
        }
    }
    
    private byte[] calculateSessionHash(byte[]... inputs) {
        Blake2bDigest digest = new Blake2bDigest(256);
        for (byte[] input : inputs) {
            digest.update(input, 0, input.length);
        }
        byte[] output = new byte[digest.getDigestSize()];
        digest.doFinal(output, 0);
        return output;
    }
    
    private byte[] blake2bHash(byte[] input, int outputLength) {
        Blake2bDigest digest = new Blake2bDigest(outputLength * 8);
        digest.update(input, 0, input.length);
        byte[] output = new byte[digest.getDigestSize()];
        digest.doFinal(output, 0);
        return output;
    }
    
    private byte[] generateRandomBytes(int length) {
        byte[] bytes = new byte[length];
        new java.security.SecureRandom().nextBytes(bytes);
        return bytes;
    }
    
    private String bytesToHex(byte[] bytes, int maxBytes) {
        StringBuilder sb = new StringBuilder();
        int limit = Math.min(bytes.length, maxBytes);
        for (int i = 0; i < limit; i++) {
            sb.append(String.format("%02x", bytes[i]));
        }
        if (bytes.length > maxBytes) {
            sb.append("...");
        }
        return sb.toString();
    }
}
