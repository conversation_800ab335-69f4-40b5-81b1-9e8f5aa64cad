package crypto.learning;

import org.bouncycastle.crypto.engines.XSalsa20Engine;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.crypto.params.ParametersWithIV;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * 安全概念演示
 * 通过实际代码演示密码学中的重要安全概念
 */
public class SecurityConceptsDemo {
    
    /**
     * 演示1：时序攻击的危险性
     */
    @Test
    public void demonstrateTimingAttack() {
        System.out.println("⏱️ 演示时序攻击的危险性");
        
        byte[] correctMAC =  {0x12, 0x34, 0x56, 0x78, 0x79, 0x22, 0x46, 0x06};
        
        // 不安全的比较方式
        long time1 = measureComparisonTime(correctMAC, new byte[]{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, false);
        long time2 = measureComparisonTime(correctMAC, new byte[]{0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, false);
        long time3 = measureComparisonTime(correctMAC, new byte[]{0x12, 0x34, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, false);
        long time4 = measureComparisonTime(correctMAC, new byte[]{0x12, 0x34, 0x56, 0x78, 0x79, 0x22, 0x46, 0x06}, false);
        
        System.out.println("不安全比较时间 (纳秒):");
        System.out.println("  全错: " + time1);
        System.out.println("  1对: " + time2);  
        System.out.println("  2对: " + time3);
        System.out.println("  全对: " + time4);
        
        // 安全的比较方式
        long safeTime1 = measureComparisonTime(correctMAC, new byte[]{0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, true);
        long safeTime2 = measureComparisonTime(correctMAC, new byte[]{0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, true);
        long safeTime3 = measureComparisonTime(correctMAC, new byte[]{0x12, 0x34, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, true);
        long safeTime4 = measureComparisonTime(correctMAC, new byte[]{0x12, 0x34, 0x56, 0x78, 0x79, 0x22, 0x46, 0x06}, true);
        
        System.out.println("安全比较时间 (纳秒):");
        System.out.println("  全错: " + safeTime1);
        System.out.println("  1对: " + safeTime2);
        System.out.println("  2对: " + safeTime3);
        System.out.println("  全对: " + safeTime4);
        
        System.out.println("✅ 可以看到安全比较的时间更加一致\n");
    }
    
    /**
     * 演示2：比特翻转攻击
     */
    @Test
    public void demonstrateBitFlippingAttack() {
        System.out.println("🔄 演示比特翻转攻击");
        
        byte[] key = generateRandomBytes(32);
        byte[] iv = generateRandomBytes(24);
        
        // 原始消息：已知格式的金融交易
        String originalMessage = "PAY:ALICE:0100:USD";
        System.out.println("原始消息: " + originalMessage);
        
        // 仅使用XSalsa20加密（没有MAC保护）
        byte[] encrypted = xsalsa20OnlyEncrypt(originalMessage.getBytes(), key, iv);
        
        // 攻击者进行比特翻转攻击
        // 目标：将"ALICE"改为"BOBBY"，将"0100"改为"9999"
        byte[] tampered = encrypted.clone();
        
        // 修改收款人：ALICE -> BOBBY
        // 已知位置：PAY:ALICE -> PAY:BOBBY
        tampered[4] ^= ('A' ^ 'B');  // A -> B
        tampered[5] ^= ('L' ^ 'O');  // L -> O  
        tampered[6] ^= ('I' ^ 'B');  // I -> B
        tampered[7] ^= ('C' ^ 'B');  // C -> B
        tampered[8] ^= ('E' ^ 'Y');  // E -> Y
        
        // 修改金额：0100 -> 9999
        tampered[10] ^= ('0' ^ '9'); // 0 -> 9
        tampered[11] ^= ('1' ^ '9'); // 1 -> 9
        tampered[12] ^= ('0' ^ '9'); // 0 -> 9
        tampered[13] ^= ('0' ^ '9'); // 0 -> 9
        
        // 解密被篡改的消息
        byte[] decrypted = xsalsa20OnlyDecrypt(tampered, key, iv);
        String tamperedMessage = new String(decrypted);
        
        System.out.println("篡改后消息: " + tamperedMessage);
        System.out.println("🚨 攻击成功！金额和收款人都被修改了");
        System.out.println("💡 这就是为什么需要MAC保护的原因\n");
    }
    
    /**
     * 演示3：MAC保护的有效性
     */
    @Test
    public void demonstrateMACProtection() {
        System.out.println("🛡️ 演示MAC保护的有效性");
        
        XSalsa20Poly1305Learning crypto = new XSalsa20Poly1305Learning();
        byte[] key = generateRandomBytes(32);
        byte[] iv = generateRandomBytes(24);
        
        String message = "PAY:ALICE:0100:USD";
        System.out.println("原始消息: " + message);
        
        // 使用认证加密
        byte[] authenticatedCiphertext = crypto.encrypt(message.getBytes(), key, iv);
        
        // 尝试篡改密文
        byte[] tampered = authenticatedCiphertext.clone();
        tampered[20] ^= 0xFF; // 随机修改一个字节
        
        try {
            crypto.decrypt(tampered, key, iv);
            fail("篡改后的密文不应该解密成功");
        } catch (SecurityException e) {
            System.out.println("✅ MAC成功检测到篡改: " + e.getMessage());
        }
        
        System.out.println("💡 认证加密有效防止了篡改攻击\n");
    }
    
    /**
     * 演示4：随机数重复的危险
     */
    @Test
    public void demonstrateNonceReuse() {
        System.out.println("🎲 演示随机数重复的危险");
        
        byte[] key = generateRandomBytes(32);
        byte[] iv = generateRandomBytes(24); // 相同的IV
        
        String message1 = "Secret message 1";
        String message2 = "Secret message 2";
        
        // 使用相同的密钥和IV加密两条消息（危险！）
        byte[] encrypted1 = xsalsa20OnlyEncrypt(message1.getBytes(), key, iv);
        byte[] encrypted2 = xsalsa20OnlyEncrypt(message2.getBytes(), key, iv);
        
        System.out.println("消息1: " + message1);
        System.out.println("消息2: " + message2);
        
        // 攻击者可以通过XOR两个密文来消除密钥流
        byte[] xorResult = new byte[Math.min(encrypted1.length, encrypted2.length)];
        for (int i = 0; i < xorResult.length; i++) {
            xorResult[i] = (byte)(encrypted1[i] ^ encrypted2[i]);
        }
        
        // 这等价于两个明文的XOR，可能泄露信息
        System.out.println("密文1 XOR 密文2 = 明文1 XOR 明文2");
        System.out.println("🚨 这可能泄露明文信息！");
        System.out.println("💡 这就是为什么每次加密都要使用不同的IV\n");
    }
    
    // ========== 辅助方法 ==========
    
    /**
     * 测量比较时间
     */
    private long measureComparisonTime(byte[] correct, byte[] test, boolean constantTime) {
        // 预热JVM
        for (int i = 0; i < 1000; i++) {
            if (constantTime) {
                constantTimeEquals(correct, test);
            } else {
                unsafeEquals(correct, test);
            }
        }
        
        // 实际测量
        long start = System.nanoTime();
        for (int i = 0; i < 10000; i++) {
            if (constantTime) {
                constantTimeEquals(correct, test);
            } else {
                unsafeEquals(correct, test);
            }
        }
        long end = System.nanoTime();
        
        return (end - start) / 10000; // 平均时间
    }
    
    /**
     * 不安全的比较（会泄露时序信息）
     */
    private boolean unsafeEquals(byte[] a, byte[] b) {
        if (a.length != b.length) return false;
        for (int i = 0; i < a.length; i++) {
            if (a[i] != b[i]) return false; // 早期退出！
        }
        return true;
    }
    
    /**
     * 安全的常量时间比较
     */
    private boolean constantTimeEquals(byte[] a, byte[] b) {
        if (a.length != b.length) return false;
        int result = 0;
        for (int i = 0; i < a.length; i++) {
            result |= a[i] ^ b[i];
        }
        return result == 0;
    }
    
    /**
     * 仅XSalsa20加密（不安全，仅用于演示）
     */
    private byte[] xsalsa20OnlyEncrypt(byte[] plaintext, byte[] key, byte[] iv) {
        try {
            XSalsa20Engine engine = new XSalsa20Engine();
            KeyParameter keyParam = new KeyParameter(key);
            ParametersWithIV params = new ParametersWithIV(keyParam, iv);
            engine.init(true, params);
            
            byte[] output = new byte[plaintext.length];
            engine.processBytes(plaintext, 0, plaintext.length, output, 0);
            return output;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    /**
     * 仅XSalsa20解密（不安全，仅用于演示）
     */
    private byte[] xsalsa20OnlyDecrypt(byte[] ciphertext, byte[] key, byte[] iv) {
        try {
            XSalsa20Engine engine = new XSalsa20Engine();
            KeyParameter keyParam = new KeyParameter(key);
            ParametersWithIV params = new ParametersWithIV(keyParam, iv);
            engine.init(false, params);
            
            byte[] output = new byte[ciphertext.length];
            engine.processBytes(ciphertext, 0, ciphertext.length, output, 0);
            return output;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    
    private byte[] generateRandomBytes(int length) {
        byte[] bytes = new byte[length];
        new java.security.SecureRandom().nextBytes(bytes);
        return bytes;
    }
}
