package crypto.learning;

import org.bouncycastle.crypto.digests.Blake2bDigest;
import org.junit.Test;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;

/**
 * 密钥管理演示
 * 
 * 演示共享密钥和通信密钥的生成、派生和使用过程
 */
public class KeyManagementDemo {
    
    private final SecureRandom random = new SecureRandom();
    
    /**
     * 演示1：基本的密钥协商和派生过程
     */
    @Test
    public void demonstrateBasicKeyDerivation() {
        System.out.println("🔑 演示基本密钥协商和派生过程");
        System.out.println("=" .repeat(50));
        
        // === 第一步：模拟已协商的共享密钥 ===
        System.out.println("👥 第一步：模拟Alice和Bob已完成密钥协商");
        
        // 在真实场景中，这个共享密钥是通过ECDH获得的
        // 这里我们直接使用一个示例共享密钥
        byte[] sharedSecret = blake2bHash("DEMO_SHARED_SECRET".getBytes(), 32);
        
        System.out.println("✅ 获得共享密钥");
        System.out.println("   共享密钥长度: " + sharedSecret.length + " 字节");
        System.out.println("   共享密钥前8字节: " + bytesToHex(sharedSecret, 8));
        
        // === 第二步：从共享密钥派生专用密钥 ===
        System.out.println("\n🔄 第二步：派生专用密钥");
        
        // 使用已获得的共享密钥
        
        // 派生不同用途的密钥
        byte[] encryptionKey = deriveKey("ENCRYPTION", sharedSecret);
        byte[] macKey = deriveKey("MAC", sharedSecret);
        byte[] metadataKey = deriveKey("METADATA", sharedSecret);
        
        System.out.println("✅ 加密密钥: " + bytesToHex(encryptionKey, 8));
        System.out.println("✅ MAC密钥: " + bytesToHex(macKey, 8));
        System.out.println("✅ 元数据密钥: " + bytesToHex(metadataKey, 8));
        
        // 验证不同密钥确实不同
        assertFalse("加密密钥和MAC密钥应该不同", 
                   Arrays.equals(encryptionKey, macKey));
        assertFalse("MAC密钥和元数据密钥应该不同", 
                   Arrays.equals(macKey, metadataKey));
        
        System.out.println("✅ 所有派生密钥都不相同");
        
        // === 第三步：演示密钥的确定性 ===
        System.out.println("\n🎯 第三步：验证密钥派生的确定性");
        
        // 重新派生相同的密钥
        byte[] encryptionKey2 = deriveKey("ENCRYPTION", sharedSecret);
        byte[] macKey2 = deriveKey("MAC", sharedSecret);
        
        assertArrayEquals("相同输入应产生相同的加密密钥", encryptionKey, encryptionKey2);
        assertArrayEquals("相同输入应产生相同的MAC密钥", macKey, macKey2);
        
        System.out.println("✅ 密钥派生具有确定性");
        System.out.println("✅ 基本密钥管理演示完成\n");
    }
    
    /**
     * 演示2：分层密钥结构
     */
    @Test
    public void demonstrateHierarchicalKeys() {
        System.out.println("🏗️ 演示分层密钥结构");
        System.out.println("=" .repeat(50));
        
        // 根密钥（共享密钥）
        byte[] rootKey = generateRandomBytes(32);
        System.out.println("🌳 根密钥: " + bytesToHex(rootKey, 8));
        
        // 第一层：协议级密钥
        byte[] chatProtocolKey = deriveKey("CHAT_PROTOCOL", rootKey);
        byte[] fileProtocolKey = deriveKey("FILE_PROTOCOL", rootKey);
        byte[] voiceProtocolKey = deriveKey("VOICE_PROTOCOL", rootKey);
        
        System.out.println("\n📱 第一层 - 协议级密钥:");
        System.out.println("  聊天协议: " + bytesToHex(chatProtocolKey, 8));
        System.out.println("  文件协议: " + bytesToHex(fileProtocolKey, 8));
        System.out.println("  语音协议: " + bytesToHex(voiceProtocolKey, 8));
        
        // 第二层：功能级密钥（以聊天协议为例）
        byte[] chatEncryptKey = deriveKey("ENCRYPT", chatProtocolKey);
        byte[] chatMacKey = deriveKey("MAC", chatProtocolKey);
        byte[] chatMetadataKey = deriveKey("METADATA", chatProtocolKey);
        
        System.out.println("\n💬 第二层 - 聊天功能密钥:");
        System.out.println("  消息加密: " + bytesToHex(chatEncryptKey, 8));
        System.out.println("  消息认证: " + bytesToHex(chatMacKey, 8));
        System.out.println("  元数据保护: " + bytesToHex(chatMetadataKey, 8));
        
        // 第三层：会话级密钥
        byte[] session1Key = deriveKey("SESSION_1", chatEncryptKey);
        byte[] session2Key = deriveKey("SESSION_2", chatEncryptKey);
        byte[] session3Key = deriveKey("SESSION_3", chatEncryptKey);
        
        System.out.println("\n🔄 第三层 - 会话级密钥:");
        System.out.println("  会话1: " + bytesToHex(session1Key, 8));
        System.out.println("  会话2: " + bytesToHex(session2Key, 8));
        System.out.println("  会话3: " + bytesToHex(session3Key, 8));
        
        // 验证密钥隔离
        assertFalse("不同协议的密钥应该不同", 
                   Arrays.equals(chatProtocolKey, fileProtocolKey));
        assertFalse("不同功能的密钥应该不同", 
                   Arrays.equals(chatEncryptKey, chatMacKey));
        assertFalse("不同会话的密钥应该不同", 
                   Arrays.equals(session1Key, session2Key));
        
        System.out.println("✅ 分层密钥结构验证完成\n");
    }
    
    /**
     * 演示3：密钥轮换和前向安全
     */
    @Test
    public void demonstrateKeyRotationAndForwardSecrecy() {
        System.out.println("🔄 演示密钥轮换和前向安全");
        System.out.println("=" .repeat(50));
        
        // 模拟长期通信中的密钥轮换
        byte[] masterKey = generateRandomBytes(32);
        System.out.println("🔑 主密钥: " + bytesToHex(masterKey, 8));
        
        // 生成多个时期的会话密钥
        Map<Integer, byte[]> sessionKeys = new HashMap<>();
        for (int epoch = 1; epoch <= 5; epoch++) {
            byte[] sessionKey = deriveKey("SESSION_EPOCH_" + epoch, masterKey);
            sessionKeys.put(epoch, sessionKey);
            System.out.println("📅 时期" + epoch + "密钥: " + bytesToHex(sessionKey, 8));
        }
        
        // 模拟消息加密
        XSalsa20Poly1305Learning crypto = new XSalsa20Poly1305Learning();
        Map<Integer, byte[]> encryptedMessages = new HashMap<>();
        byte[] iv = generateRandomBytes(24);

        System.out.println("\n📝 加密不同时期的消息:");
        for (int epoch = 1; epoch <= 5; epoch++) {
            String message = "时期" + epoch + "的秘密消息";
            
            byte[] encrypted = crypto.encrypt(message.getBytes(), 
                                            sessionKeys.get(epoch), iv);
            encryptedMessages.put(epoch, encrypted);
            System.out.println("  时期" + epoch + ": " + message + " -> 已加密");
        }
        
        // 模拟密钥泄露：假设时期3的密钥被泄露
        System.out.println("\n🚨 模拟安全事件：时期3的密钥被泄露");
        byte[] compromisedKey = sessionKeys.get(3);
        
        // 攻击者只能解密时期3的消息
        try {
            byte[] decrypted = crypto.decrypt(encryptedMessages.get(3), 
                                            compromisedKey, iv);
            System.out.println("⚠️ 时期3消息被解密（" + new String(decrypted) + "）");
        } catch (Exception e) {
            // 这里简化处理，实际中需要正确的IV
        }
        
        // 但无法解密其他时期的消息
        for (int epoch : Arrays.asList(1, 2, 4, 5)) {
            try {
                crypto.decrypt(encryptedMessages.get(epoch), 
                              compromisedKey, iv);
                fail("不应该能用泄露的密钥解密其他时期的消息");
            } catch (Exception e) {
                System.out.println("✅ 时期" + epoch + "消息保持安全");
            }
        }
        
        System.out.println("✅ 前向安全性验证完成：密钥泄露只影响对应时期\n");
    }
    
    /**
     * 演示4：上下文绑定的密钥派生
     */
    @Test
    public void demonstrateContextBoundKeyDerivation() {
        System.out.println("🎯 演示上下文绑定的密钥派生");
        System.out.println("=" .repeat(50));
        
        byte[] masterKey = generateRandomBytes(32);
        
        // 不同上下文的密钥派生
        String[] contexts = {
            "ALICE_TO_BOB_CHAT",
            "BOB_TO_ALICE_CHAT", 
            "GROUP_CHAT_123",
            "FILE_TRANSFER_456",
            "VOICE_CALL_789"
        };
        
        System.out.println("🏷️ 为不同上下文派生密钥:");
        Map<String, byte[]> contextKeys = new HashMap<>();
        
        for (String context : contexts) {
            byte[] contextKey = deriveKey(context, masterKey);
            contextKeys.put(context, contextKey);
            System.out.println("  " + context + ": " + bytesToHex(contextKey, 8));
        }
        
        // 验证上下文隔离
        System.out.println("\n🔒 验证上下文隔离:");
        for (int i = 0; i < contexts.length; i++) {
            for (int j = i + 1; j < contexts.length; j++) {
                String context1 = contexts[i];
                String context2 = contexts[j];
                assertFalse("不同上下文应该产生不同密钥: " + context1 + " vs " + context2,
                           Arrays.equals(contextKeys.get(context1), contextKeys.get(context2)));
            }
        }
        System.out.println("✅ 所有上下文密钥都是唯一的");
        
        // 演示双向通信的密钥不对称性
        byte[] aliceToBob = contextKeys.get("ALICE_TO_BOB_CHAT");
        byte[] bobToAlice = contextKeys.get("BOB_TO_ALICE_CHAT");
        
        assertFalse("双向通信应该使用不同的密钥", 
                   Arrays.equals(aliceToBob, bobToAlice));
        System.out.println("✅ 双向通信密钥不对称性验证完成\n");
    }
    
    // ========== 辅助方法 ==========
    
    /**
     * 模拟ECDH密钥协商
     */
    private byte[] performECDH(byte[] privateKey, byte[] publicKey) {
        // 简化的ECDH实现：确保满足交换律 performECDH(a,B) = performECDH(b,A)
        // 通过确保计算过程完全对称来实现这一点
        
        // 将两个密钥都包含在一个对称的哈希计算中
        byte[] combined = new byte[64];
        
        // 确保顺序不影响结果：将较小的密钥放在前面
        if (compareArrays(privateKey, publicKey) <= 0) {
            System.arraycopy(privateKey, 0, combined, 0, 32);
            System.arraycopy(publicKey, 0, combined, 32, 32);
        } else {
            System.arraycopy(publicKey, 0, combined, 0, 32);
            System.arraycopy(privateKey, 0, combined, 32, 32);
        }
        
        // 使用确定性哈希生成共享密钥
        return blake2bHash(combined, 32);
    }
    
    /**
     * 比较两个字节数组，返回字典序比较结果
     */
    private int compareArrays(byte[] a, byte[] b) {
        for (int i = 0; i < Math.min(a.length, b.length); i++) {
            int diff = (a[i] & 0xFF) - (b[i] & 0xFF);
            if (diff != 0) {
                return diff;
            }
        }
        return a.length - b.length;
    }
    
    /**
     * 从私钥派生公钥（简化版）
     */
    private byte[] derivePublicKey(byte[] privateKey) {
        // 简化实现：实际应该使用椭圆曲线点乘
        return blake2bHash(privateKey, 32);
    }
    
    /**
     * 密钥派生函数
     */
    private byte[] deriveKey(String label, byte[] secret) {
        byte[] labelBytes = label.getBytes();
        byte[] combined = new byte[labelBytes.length + secret.length];
        System.arraycopy(labelBytes, 0, combined, 0, labelBytes.length);
        System.arraycopy(secret, 0, combined, labelBytes.length, secret.length);
        return blake2bHash(combined, 32);
    }
    
    /**
     * Blake2b哈希函数
     */
    private byte[] blake2bHash(byte[] input, int outputLength) {
        Blake2bDigest digest = new Blake2bDigest(outputLength * 8);
        digest.update(input, 0, input.length);
        byte[] output = new byte[digest.getDigestSize()];
        digest.doFinal(output, 0);
        return output;
    }
    
    /**
     * 生成随机字节
     */
    private byte[] generateRandomBytes(int length) {
        byte[] bytes = new byte[length];
        random.nextBytes(bytes);
        return bytes;
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes, int maxBytes) {
        StringBuilder sb = new StringBuilder();
        int limit = Math.min(bytes.length, maxBytes);
        for (int i = 0; i < limit; i++) {
            sb.append(String.format("%02x", bytes[i]));
        }
        if (bytes.length > maxBytes) {
            sb.append("...");
        }
        return sb.toString();
    }
}
