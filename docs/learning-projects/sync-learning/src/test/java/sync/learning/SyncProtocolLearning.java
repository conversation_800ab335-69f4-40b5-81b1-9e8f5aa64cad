package sync.learning;

import org.junit.Test;
import org.junit.Before;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;
import java.io.*;
import java.nio.ByteBuffer;
import static org.junit.Assert.*;

/**
 * 同步协议学习实践
 * 
 * 学习目标：
 * 1. 理解Briar的同步协议设计
 * 2. 掌握不同类型的同步记录
 * 3. 学习消息同步的完整流程
 * 4. 实现简化版的同步协议
 */
public class SyncProtocolLearning {
    
    private SyncManager syncManager;
    private MessageStore messageStore;
    private ContactManager contactManager;
    
    @Before
    public void setUp() {
        syncManager = new SyncManager();
        messageStore = new MessageStore();
        contactManager = new ContactManager();
    }
    
    /**
     * 测试1：同步记录类型和序列化
     */
    @Test
    public void testSyncRecordTypes() {
        System.out.println("📦 开始同步记录类型测试");
        System.out.println("=" .repeat(50));
        
        // 创建不同类型的同步记录
        System.out.println("🔧 创建不同类型的同步记录:");
        
        // 1. 版本记录
        List<Byte> supportedVersions = Arrays.asList((byte)0, (byte)1);
        VersionsRecord versions = new VersionsRecord(supportedVersions);
        
        byte[] versionsData = versions.serialize();
        System.out.println("  ✅ 版本记录: " + versionsData.length + " 字节");
        System.out.println("     支持版本: " + supportedVersions);
        
        // 2. 优先级记录
        PriorityRecord priority = new PriorityRecord();
        byte[] priorityData = priority.serialize();
        System.out.println("  ✅ 优先级记录: " + priorityData.length + " 字节");
        System.out.println("     随机数: " + bytesToHex(priority.getNonce(), 8));
        
        // 3. Offer记录
        List<String> messageIds = Arrays.asList("msg001", "msg002", "msg003");
        OfferRecord offer = new OfferRecord(messageIds);
        byte[] offerData = offer.serialize();
        System.out.println("  ✅ Offer记录: " + offerData.length + " 字节");
        System.out.println("     消息数量: " + messageIds.size());
        
        // 4. Request记录
        List<String> requestIds = Arrays.asList("msg001", "msg003");
        RequestRecord request = new RequestRecord(requestIds);
        byte[] requestData = request.serialize();
        System.out.println("  ✅ Request记录: " + requestData.length + " 字节");
        System.out.println("     请求数量: " + requestIds.size());
        
        // 5. Message记录
        MessageRecord message = new MessageRecord("msg001", "group1", 
                System.currentTimeMillis(), "Hello, Briar Sync!".getBytes());
        byte[] messageData = message.serialize();
        System.out.println("  ✅ Message记录: " + messageData.length + " 字节");
        System.out.println("     消息ID: " + message.getMessageId());
        System.out.println("     消息长度: " + message.getBody().length + " 字节");
        
        // 6. Ack记录
        List<String> ackIds = Arrays.asList("msg001", "msg002");
        AckRecord ack = new AckRecord(ackIds);
        byte[] ackData = ack.serialize();
        System.out.println("  ✅ Ack记录: " + ackData.length + " 字节");
        System.out.println("     确认数量: " + ackIds.size());
        
        System.out.println("\n🔍 测试记录的序列化和反序列化:");
        
        // 测试序列化和反序列化
        VersionsRecord deserializedVersions = VersionsRecord.deserialize(versionsData);
        assertEquals("版本记录应该一致", versions.getSupportedVersions(), 
                    deserializedVersions.getSupportedVersions());
        
        OfferRecord deserializedOffer = OfferRecord.deserialize(offerData);
        assertEquals("Offer记录应该一致", offer.getMessageIds(), 
                    deserializedOffer.getMessageIds());
        
        MessageRecord deserializedMessage = MessageRecord.deserialize(messageData);
        assertEquals("消息ID应该一致", message.getMessageId(), 
                    deserializedMessage.getMessageId());
        assertArrayEquals("消息体应该一致", message.getBody(), 
                         deserializedMessage.getBody());
        
        System.out.println("  ✅ 版本记录序列化/反序列化成功");
        System.out.println("  ✅ Offer记录序列化/反序列化成功");
        System.out.println("  ✅ Message记录序列化/反序列化成功");
        
        System.out.println("✅ 同步记录类型测试完成\n");
    }
    
    /**
     * 测试2：同步会话建立过程
     */
    @Test
    public void testSyncSessionEstablishment() {
        System.out.println("🤝 开始同步会话建立测试");
        System.out.println("=" .repeat(50));
        
        // 模拟Alice和Bob的同步会话建立
        System.out.println("👥 模拟Alice和Bob的同步会话:");
        
        SyncSession aliceSession = new SyncSession("Alice", true);
        SyncSession bobSession = new SyncSession("Bob", false);
        
        System.out.println("  📤 第一步：版本协商");
        
        // Alice发送支持的版本
        VersionsRecord aliceVersions = aliceSession.createVersionsRecord();
        byte[] aliceVersionsData = aliceVersions.serialize();
        
        System.out.println("     Alice支持版本: " + aliceVersions.getSupportedVersions());
        
        // Bob接收并响应版本
        VersionsRecord receivedVersions = VersionsRecord.deserialize(aliceVersionsData);
        boolean versionCompatible = bobSession.processVersionsRecord(receivedVersions);
        assertTrue("版本应该兼容", versionCompatible);
        
        VersionsRecord bobVersions = bobSession.createVersionsRecord();
        byte[] bobVersionsData = bobVersions.serialize();
        
        System.out.println("     Bob支持版本: " + bobVersions.getSupportedVersions());
        
        // Alice确认版本
        VersionsRecord bobReceived = VersionsRecord.deserialize(bobVersionsData);
        boolean aliceVersionCompatible = aliceSession.processVersionsRecord(bobReceived);
        assertTrue("Alice应该接受Bob的版本", aliceVersionCompatible);
        
        System.out.println("     ✅ 版本协商成功");
        
        System.out.println("  🎲 第二步：优先级交换");
        
        // 交换优先级（避免冗余连接）
        PriorityRecord alicePriority = aliceSession.createPriorityRecord();
        PriorityRecord bobPriority = bobSession.createPriorityRecord();
        
        System.out.println("     Alice优先级: " + bytesToHex(alicePriority.getNonce(), 8));
        System.out.println("     Bob优先级: " + bytesToHex(bobPriority.getNonce(), 8));
        
        // 比较优先级决定谁是主导方
        int priorityComparison = alicePriority.compareTo(bobPriority);
        String leader = priorityComparison > 0 ? "Alice" : "Bob";
        System.out.println("     ✅ 主导方: " + leader);
        
        // 验证会话状态
        assertEquals("Alice会话应该已建立", SyncSessionState.ESTABLISHED, aliceSession.getState());
        assertEquals("Bob会话应该已建立", SyncSessionState.ESTABLISHED, bobSession.getState());
        
        System.out.println("  🎉 会话建立完成");
        System.out.println("     Alice状态: " + aliceSession.getState());
        System.out.println("     Bob状态: " + bobSession.getState());
        
        System.out.println("✅ 同步会话建立测试完成\n");
    }
    
    /**
     * 测试3：消息同步流程
     */
    @Test
    public void testMessageSyncFlow() {
        System.out.println("📡 开始消息同步流程测试");
        System.out.println("=" .repeat(50));
        
        // 准备测试数据
        String contactId = "contact_alice";
        
        // Alice有一些消息要发送给Bob
        List<MessageRecord> aliceMessages = Arrays.asList(
            new MessageRecord("msg001", "group1", System.currentTimeMillis(), "消息1".getBytes()),
            new MessageRecord("msg002", "group1", System.currentTimeMillis() + 1000, "消息2".getBytes()),
            new MessageRecord("msg003", "group2", System.currentTimeMillis() + 2000, "消息3".getBytes())
        );
        
        // Bob有一些消息要发送给Alice
        List<MessageRecord> bobMessages = Arrays.asList(
            new MessageRecord("msg004", "group1", System.currentTimeMillis() + 3000, "消息4".getBytes()),
            new MessageRecord("msg005", "group2", System.currentTimeMillis() + 4000, "消息5".getBytes())
        );
        
        // 将消息添加到各自的存储
        for (MessageRecord msg : aliceMessages) {
            messageStore.addMessage("Alice", msg);
        }
        for (MessageRecord msg : bobMessages) {
            messageStore.addMessage("Bob", msg);
        }
        
        System.out.println("📋 初始状态:");
        System.out.println("  Alice有 " + aliceMessages.size() + " 条消息");
        System.out.println("  Bob有 " + bobMessages.size() + " 条消息");
        
        // 第一步：Alice发送Offer
        System.out.println("\n📤 第一步：Alice发送Offer");
        
        List<String> aliceMessageIds = aliceMessages.stream()
                .map(MessageRecord::getMessageId)
                .collect(Collectors.toList());
        OfferRecord aliceOffer = new OfferRecord(aliceMessageIds);
        
        System.out.println("  Alice提供消息: " + aliceOffer.getMessageIds());
        
        // 第二步：Bob处理Offer并发送Request
        System.out.println("\n📥 第二步：Bob处理Offer并发送Request");
        
        List<String> bobWants = aliceOffer.getMessageIds().stream()
                .filter(id -> !messageStore.hasMessage("Bob", id))
                .collect(Collectors.toList());
        
        RequestRecord bobRequest = new RequestRecord(bobWants);
        System.out.println("  Bob请求消息: " + bobRequest.getMessageIds());
        
        // 第三步：Alice发送请求的消息
        System.out.println("\n📦 第三步：Alice发送请求的消息");
        
        List<MessageRecord> sentMessages = new ArrayList<>();
        for (String messageId : bobRequest.getMessageIds()) {
            MessageRecord message = messageStore.getMessage("Alice", messageId);
            if (message != null) {
                sentMessages.add(message);
                System.out.println("  发送消息: " + messageId + " (" + 
                                 new String(message.getBody()) + ")");
            }
        }
        
        // 第四步：Bob接收消息并发送Ack
        System.out.println("\n✅ 第四步：Bob接收消息并发送Ack");
        
        List<String> receivedIds = new ArrayList<>();
        for (MessageRecord message : sentMessages) {
            messageStore.addMessage("Bob", message);
            receivedIds.add(message.getMessageId());
            System.out.println("  接收消息: " + message.getMessageId() + " (" + 
                             new String(message.getBody()) + ")");
        }
        
        AckRecord bobAck = new AckRecord(receivedIds);
        System.out.println("  Bob确认消息: " + bobAck.getMessageIds());
        
        // 第五步：反向同步（Bob发送Offer给Alice）
        System.out.println("\n🔄 第五步：反向同步");
        
        List<String> bobMessageIds = bobMessages.stream()
                .map(MessageRecord::getMessageId)
                .collect(Collectors.toList());
        OfferRecord bobOffer = new OfferRecord(bobMessageIds);
        
        List<String> aliceWants = bobOffer.getMessageIds().stream()
                .filter(id -> !messageStore.hasMessage("Alice", id))
                .collect(Collectors.toList());
        
        RequestRecord aliceRequest = new RequestRecord(aliceWants);
        
        for (String messageId : aliceRequest.getMessageIds()) {
            MessageRecord message = messageStore.getMessage("Bob", messageId);
            if (message != null) {
                messageStore.addMessage("Alice", message);
                System.out.println("  Alice接收: " + messageId + " (" + 
                                 new String(message.getBody()) + ")");
            }
        }
        
        // 验证同步结果
        System.out.println("\n🔍 验证同步结果:");
        
        int aliceFinalCount = messageStore.getMessageCount("Alice");
        int bobFinalCount = messageStore.getMessageCount("Bob");
        
        System.out.println("  Alice最终消息数: " + aliceFinalCount);
        System.out.println("  Bob最终消息数: " + bobFinalCount);
        
        assertEquals("Alice和Bob应该有相同数量的消息", aliceFinalCount, bobFinalCount);
        assertEquals("应该有5条消息", 5, aliceFinalCount);
        
        System.out.println("✅ 消息同步流程测试完成\n");
    }
    
    /**
     * 测试4：冲突解决机制
     */
    @Test
    public void testConflictResolution() {
        System.out.println("⚔️ 开始冲突解决机制测试");
        System.out.println("=" .repeat(50));
        
        ConflictResolver resolver = new ConflictResolver();
        
        // 创建冲突的消息（相同组，不同时间戳）
        long baseTime = System.currentTimeMillis();
        List<MessageRecord> conflictingMessages = Arrays.asList(
            new MessageRecord("msg001", "group1", baseTime + 3000, "第三条消息".getBytes()),
            new MessageRecord("msg002", "group1", baseTime + 1000, "第一条消息".getBytes()),
            new MessageRecord("msg003", "group1", baseTime + 2000, "第二条消息".getBytes()),
            new MessageRecord("msg004", "group1", baseTime + 1000, "同时间消息A".getBytes()),
            new MessageRecord("msg005", "group1", baseTime + 1000, "同时间消息B".getBytes())
        );
        
        System.out.println("📋 冲突消息列表:");
        for (MessageRecord msg : conflictingMessages) {
            System.out.println("  " + msg.getMessageId() + ": " + 
                             new String(msg.getBody()) + " (时间: " + msg.getTimestamp() + ")");
        }
        
        // 解决冲突
        System.out.println("\n🔧 应用冲突解决策略:");
        
        List<MessageRecord> resolvedMessages = resolver.resolveConflicts(conflictingMessages);
        
        System.out.println("📊 解决后的消息顺序:");
        for (int i = 0; i < resolvedMessages.size(); i++) {
            MessageRecord msg = resolvedMessages.get(i);
            System.out.println("  " + (i + 1) + ". " + msg.getMessageId() + ": " + 
                             new String(msg.getBody()) + " (时间: " + msg.getTimestamp() + ")");
        }
        
        // 验证排序结果
        for (int i = 1; i < resolvedMessages.size(); i++) {
            MessageRecord prev = resolvedMessages.get(i - 1);
            MessageRecord curr = resolvedMessages.get(i);
            
            assertTrue("消息应该按时间戳排序", 
                      prev.getTimestamp() <= curr.getTimestamp());
            
            // 如果时间戳相同，应该按消息ID排序
            if (prev.getTimestamp() == curr.getTimestamp()) {
                assertTrue("相同时间戳的消息应该按ID排序",
                          prev.getMessageId().compareTo(curr.getMessageId()) <= 0);
            }
        }
        
        System.out.println("✅ 冲突解决机制测试完成\n");
    }
    
    /**
     * 测试5：增量同步优化
     */
    @Test
    public void testIncrementalSync() {
        System.out.println("📈 开始增量同步优化测试");
        System.out.println("=" .repeat(50));
        
        SyncOptimizer optimizer = new SyncOptimizer();
        
        // 模拟大量消息的场景
        System.out.println("📊 创建大量消息进行测试:");
        
        List<MessageRecord> allMessages = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            MessageRecord msg = new MessageRecord(
                "msg" + String.format("%03d", i),
                "group" + (i % 5 + 1),  // 5个不同的组
                System.currentTimeMillis() + i * 1000,
                ("消息内容 " + i).getBytes()
            );
            allMessages.add(msg);
        }
        
        // Alice有前80条消息
        for (int i = 0; i < 80; i++) {
            messageStore.addMessage("Alice", allMessages.get(i));
        }
        
        // Bob有后80条消息（20-100）
        for (int i = 20; i < 100; i++) {
            messageStore.addMessage("Bob", allMessages.get(i));
        }
        
        System.out.println("  Alice有消息: 1-80 (共80条)");
        System.out.println("  Bob有消息: 21-100 (共80条)");
        System.out.println("  重叠消息: 21-80 (共60条)");
        
        // 计算需要同步的消息
        System.out.println("\n🔍 计算增量同步:");
        
        Set<String> aliceIds = messageStore.getMessageIds("Alice");
        Set<String> bobIds = messageStore.getMessageIds("Bob");
        
        Set<String> aliceNeedsToBob = new HashSet<>(aliceIds);
        aliceNeedsToBob.removeAll(bobIds);  // Alice有但Bob没有的
        
        Set<String> bobNeedsToAlice = new HashSet<>(bobIds);
        bobNeedsToAlice.removeAll(aliceIds);  // Bob有但Alice没有的
        
        System.out.println("  Alice需要发送给Bob: " + aliceNeedsToBob.size() + " 条");
        System.out.println("  Bob需要发送给Alice: " + bobNeedsToAlice.size() + " 条");
        
        // 模拟优化的同步过程
        System.out.println("\n⚡ 执行优化同步:");
        
        // 使用布隆过滤器优化（简化版）
        BloomFilter aliceFilter = optimizer.createBloomFilter(aliceIds);
        BloomFilter bobFilter = optimizer.createBloomFilter(bobIds);
        
        // 快速过滤可能需要的消息
        Set<String> aliceCandidates = optimizer.filterCandidates(bobIds, aliceFilter);
        Set<String> bobCandidates = optimizer.filterCandidates(aliceIds, bobFilter);
        
        System.out.println("  布隆过滤器候选 - Alice: " + aliceCandidates.size());
        System.out.println("  布隆过滤器候选 - Bob: " + bobCandidates.size());
        
        // 批量传输
        int batchSize = 10;
        int aliceBatches = (aliceNeedsToBob.size() + batchSize - 1) / batchSize;
        int bobBatches = (bobNeedsToAlice.size() + batchSize - 1) / batchSize;
        
        System.out.println("  Alice需要发送批次: " + aliceBatches);
        System.out.println("  Bob需要发送批次: " + bobBatches);
        
        // 验证优化效果
        int totalMessages = aliceIds.size() + bobIds.size();
        int uniqueMessages = new HashSet<String>() {{
            addAll(aliceIds);
            addAll(bobIds);
        }}.size();
        int duplicateMessages = totalMessages - uniqueMessages;
        
        double compressionRatio = (double) duplicateMessages / totalMessages;
        
        System.out.println("\n📊 同步效率统计:");
        System.out.println("  总消息数: " + totalMessages);
        System.out.println("  唯一消息数: " + uniqueMessages);
        System.out.println("  重复消息数: " + duplicateMessages);
        System.out.println("  压缩比: " + String.format("%.2f%%", compressionRatio * 100));
        
        assertTrue("应该有重复消息", duplicateMessages > 0);
        assertTrue("压缩比应该大于0", compressionRatio > 0);
        
        System.out.println("✅ 增量同步优化测试完成\n");
    }
    
    // ========== 辅助方法 ==========
    
    private String bytesToHex(byte[] bytes, int maxBytes) {
        StringBuilder sb = new StringBuilder();
        int limit = Math.min(bytes.length, maxBytes);
        for (int i = 0; i < limit; i++) {
            sb.append(String.format("%02x", bytes[i]));
        }
        if (bytes.length > maxBytes) {
            sb.append("...");
        }
        return sb.toString();
    }

    // ========== 同步记录类定义 ==========

    /**
     * 同步记录类型枚举
     */
    enum SyncRecordType {
        VERSIONS(1),
        PRIORITY(2),
        OFFER(3),
        REQUEST(4),
        MESSAGE(5),
        ACK(6),
        KEEPALIVE(7);

        private final int value;

        SyncRecordType(int value) {
            this.value = value;
        }

        public int getValue() { return value; }

        public static SyncRecordType fromValue(int value) {
            for (SyncRecordType type : values()) {
                if (type.value == value) return type;
            }
            throw new IllegalArgumentException("未知记录类型: " + value);
        }
    }

    /**
     * 抽象同步记录基类
     */
    abstract static class SyncRecord {
        protected final SyncRecordType type;

        public SyncRecord(SyncRecordType type) {
            this.type = type;
        }

        public SyncRecordType getType() { return type; }

        public abstract byte[] serialize();

        protected ByteBuffer createBuffer(int capacity) {
            ByteBuffer buffer = ByteBuffer.allocate(capacity);
            buffer.putInt(type.getValue());
            return buffer;
        }

        protected static SyncRecordType readRecordType(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            int typeValue = buffer.getInt();
            return SyncRecordType.fromValue(typeValue);
        }
    }

    /**
     * 版本记录
     */
    static class VersionsRecord extends SyncRecord {
        private final List<Byte> supportedVersions;

        public VersionsRecord(List<Byte> supportedVersions) {
            super(SyncRecordType.VERSIONS);
            this.supportedVersions = new ArrayList<>(supportedVersions);
        }

        public List<Byte> getSupportedVersions() {
            return new ArrayList<>(supportedVersions);
        }

        @Override
        public byte[] serialize() {
            ByteBuffer buffer = createBuffer(4 + 4 + supportedVersions.size());
            buffer.putInt(supportedVersions.size());
            for (Byte version : supportedVersions) {
                buffer.put(version);
            }
            return buffer.array();
        }

        public static VersionsRecord deserialize(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            SyncRecordType type = SyncRecordType.fromValue(buffer.getInt());
            if (type != SyncRecordType.VERSIONS) {
                throw new IllegalArgumentException("不是版本记录");
            }

            int count = buffer.getInt();
            List<Byte> versions = new ArrayList<>();
            for (int i = 0; i < count; i++) {
                versions.add(buffer.get());
            }

            return new VersionsRecord(versions);
        }
    }

    /**
     * 优先级记录
     */
    static class PriorityRecord extends SyncRecord implements Comparable<PriorityRecord> {
        private final byte[] nonce;

        public PriorityRecord() {
            super(SyncRecordType.PRIORITY);
            this.nonce = new byte[16];  // 16字节随机数
            new java.security.SecureRandom().nextBytes(nonce);
        }

        public PriorityRecord(byte[] nonce) {
            super(SyncRecordType.PRIORITY);
            this.nonce = nonce.clone();
        }

        public byte[] getNonce() { return nonce.clone(); }

        @Override
        public byte[] serialize() {
            ByteBuffer buffer = createBuffer(4 + nonce.length);
            buffer.put(nonce);
            return buffer.array();
        }

        public static PriorityRecord deserialize(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            SyncRecordType type = SyncRecordType.fromValue(buffer.getInt());
            if (type != SyncRecordType.PRIORITY) {
                throw new IllegalArgumentException("不是优先级记录");
            }

            byte[] nonce = new byte[16];
            buffer.get(nonce);
            return new PriorityRecord(nonce);
        }

        @Override
        public int compareTo(PriorityRecord other) {
            // 比较随机数的无符号值
            for (int i = 0; i < nonce.length; i++) {
                int thisUnsigned = nonce[i] & 0xFF;
                int otherUnsigned = other.nonce[i] & 0xFF;
                if (thisUnsigned != otherUnsigned) {
                    return Integer.compare(thisUnsigned, otherUnsigned);
                }
            }
            return 0;
        }
    }

    /**
     * Offer记录
     */
    static class OfferRecord extends SyncRecord {
        private final List<String> messageIds;

        public OfferRecord(List<String> messageIds) {
            super(SyncRecordType.OFFER);
            this.messageIds = new ArrayList<>(messageIds);
        }

        public List<String> getMessageIds() {
            return new ArrayList<>(messageIds);
        }

        @Override
        public byte[] serialize() {
            // 计算总长度
            int totalLength = 4 + 4;  // 类型 + 数量
            for (String id : messageIds) {
                totalLength += 4 + id.getBytes().length;  // 长度 + 内容
            }

            ByteBuffer buffer = createBuffer(totalLength);
            buffer.putInt(messageIds.size());

            for (String id : messageIds) {
                byte[] idBytes = id.getBytes();
                buffer.putInt(idBytes.length);
                buffer.put(idBytes);
            }

            return buffer.array();
        }

        public static OfferRecord deserialize(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            SyncRecordType type = SyncRecordType.fromValue(buffer.getInt());
            if (type != SyncRecordType.OFFER) {
                throw new IllegalArgumentException("不是Offer记录");
            }

            int count = buffer.getInt();
            List<String> messageIds = new ArrayList<>();

            for (int i = 0; i < count; i++) {
                int idLength = buffer.getInt();
                byte[] idBytes = new byte[idLength];
                buffer.get(idBytes);
                messageIds.add(new String(idBytes));
            }

            return new OfferRecord(messageIds);
        }
    }

    /**
     * Request记录
     */
    static class RequestRecord extends SyncRecord {
        private final List<String> messageIds;

        public RequestRecord(List<String> messageIds) {
            super(SyncRecordType.REQUEST);
            this.messageIds = new ArrayList<>(messageIds);
        }

        public List<String> getMessageIds() {
            return new ArrayList<>(messageIds);
        }

        @Override
        public byte[] serialize() {
            // 与OfferRecord类似的序列化逻辑
            int totalLength = 4 + 4;
            for (String id : messageIds) {
                totalLength += 4 + id.getBytes().length;
            }

            ByteBuffer buffer = createBuffer(totalLength);
            buffer.putInt(messageIds.size());

            for (String id : messageIds) {
                byte[] idBytes = id.getBytes();
                buffer.putInt(idBytes.length);
                buffer.put(idBytes);
            }

            return buffer.array();
        }

        public static RequestRecord deserialize(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            SyncRecordType type = SyncRecordType.fromValue(buffer.getInt());
            if (type != SyncRecordType.REQUEST) {
                throw new IllegalArgumentException("不是Request记录");
            }

            int count = buffer.getInt();
            List<String> messageIds = new ArrayList<>();

            for (int i = 0; i < count; i++) {
                int idLength = buffer.getInt();
                byte[] idBytes = new byte[idLength];
                buffer.get(idBytes);
                messageIds.add(new String(idBytes));
            }

            return new RequestRecord(messageIds);
        }
    }

    /**
     * Message记录
     */
    static class MessageRecord extends SyncRecord {
        private final String messageId;
        private final String groupId;
        private final long timestamp;
        private final byte[] body;

        public MessageRecord(String messageId, String groupId, long timestamp, byte[] body) {
            super(SyncRecordType.MESSAGE);
            this.messageId = messageId;
            this.groupId = groupId;
            this.timestamp = timestamp;
            this.body = body.clone();
        }

        public String getMessageId() { return messageId; }
        public String getGroupId() { return groupId; }
        public long getTimestamp() { return timestamp; }
        public byte[] getBody() { return body.clone(); }

        @Override
        public byte[] serialize() {
            byte[] messageIdBytes = messageId.getBytes();
            byte[] groupIdBytes = groupId.getBytes();

            int totalLength = 4 + 4 + messageIdBytes.length + 4 + groupIdBytes.length + 8 + 4 + body.length;
            ByteBuffer buffer = createBuffer(totalLength);

            // 消息ID
            buffer.putInt(messageIdBytes.length);
            buffer.put(messageIdBytes);

            // 组ID
            buffer.putInt(groupIdBytes.length);
            buffer.put(groupIdBytes);

            // 时间戳
            buffer.putLong(timestamp);

            // 消息体
            buffer.putInt(body.length);
            buffer.put(body);

            return buffer.array();
        }

        public static MessageRecord deserialize(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            SyncRecordType type = SyncRecordType.fromValue(buffer.getInt());
            if (type != SyncRecordType.MESSAGE) {
                throw new IllegalArgumentException("不是Message记录");
            }

            // 消息ID
            int messageIdLength = buffer.getInt();
            byte[] messageIdBytes = new byte[messageIdLength];
            buffer.get(messageIdBytes);
            String messageId = new String(messageIdBytes);

            // 组ID
            int groupIdLength = buffer.getInt();
            byte[] groupIdBytes = new byte[groupIdLength];
            buffer.get(groupIdBytes);
            String groupId = new String(groupIdBytes);

            // 时间戳
            long timestamp = buffer.getLong();

            // 消息体
            int bodyLength = buffer.getInt();
            byte[] body = new byte[bodyLength];
            buffer.get(body);

            return new MessageRecord(messageId, groupId, timestamp, body);
        }
    }

    /**
     * Ack记录
     */
    static class AckRecord extends SyncRecord {
        private final List<String> messageIds;

        public AckRecord(List<String> messageIds) {
            super(SyncRecordType.ACK);
            this.messageIds = new ArrayList<>(messageIds);
        }

        public List<String> getMessageIds() {
            return new ArrayList<>(messageIds);
        }

        @Override
        public byte[] serialize() {
            int totalLength = 4 + 4;
            for (String id : messageIds) {
                totalLength += 4 + id.getBytes().length;
            }

            ByteBuffer buffer = createBuffer(totalLength);
            buffer.putInt(messageIds.size());

            for (String id : messageIds) {
                byte[] idBytes = id.getBytes();
                buffer.putInt(idBytes.length);
                buffer.put(idBytes);
            }

            return buffer.array();
        }

        public static AckRecord deserialize(byte[] data) {
            ByteBuffer buffer = ByteBuffer.wrap(data);
            SyncRecordType type = SyncRecordType.fromValue(buffer.getInt());
            if (type != SyncRecordType.ACK) {
                throw new IllegalArgumentException("不是Ack记录");
            }

            int count = buffer.getInt();
            List<String> messageIds = new ArrayList<>();

            for (int i = 0; i < count; i++) {
                int idLength = buffer.getInt();
                byte[] idBytes = new byte[idLength];
                buffer.get(idBytes);
                messageIds.add(new String(idBytes));
            }

            return new AckRecord(messageIds);
        }
    }

    // ========== 同步会话和管理器类 ==========

    /**
     * 同步会话状态
     */
    enum SyncSessionState {
        DISCONNECTED,
        CONNECTING,
        NEGOTIATING,
        ESTABLISHED,
        CLOSING,
        CLOSED
    }

    /**
     * 同步会话
     */
    static class SyncSession {
        private final String nodeId;
        private final boolean isInitiator;
        private SyncSessionState state = SyncSessionState.DISCONNECTED;
        private List<Byte> supportedVersions = Arrays.asList((byte)0, (byte)1);
        private Byte negotiatedVersion;
        private PriorityRecord priority;

        public SyncSession(String nodeId, boolean isInitiator) {
            this.nodeId = nodeId;
            this.isInitiator = isInitiator;
            this.priority = new PriorityRecord();
        }

        public String getNodeId() { return nodeId; }
        public boolean isInitiator() { return isInitiator; }
        public SyncSessionState getState() { return state; }
        public void setState(SyncSessionState state) { this.state = state; }

        public VersionsRecord createVersionsRecord() {
            return new VersionsRecord(supportedVersions);
        }

        public boolean processVersionsRecord(VersionsRecord record) {
            // 找到共同支持的最高版本
            for (Byte version : supportedVersions) {
                if (record.getSupportedVersions().contains(version)) {
                    negotiatedVersion = version;
                    state = SyncSessionState.ESTABLISHED;
                    return true;
                }
            }
            return false;  // 无兼容版本
        }

        public PriorityRecord createPriorityRecord() {
            return priority;
        }

        public Byte getNegotiatedVersion() { return negotiatedVersion; }
    }

    /**
     * 同步管理器
     */
    static class SyncManager {
        private final Map<String, SyncSession> sessions = new ConcurrentHashMap<>();
        private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

        public void registerSession(SyncSession session) {
            sessions.put(session.getNodeId(), session);
        }

        public SyncSession getSession(String nodeId) {
            return sessions.get(nodeId);
        }

        public void removeSession(String nodeId) {
            sessions.remove(nodeId);
        }

        public Collection<SyncSession> getActiveSessions() {
            return sessions.values().stream()
                    .filter(session -> session.getState() == SyncSessionState.ESTABLISHED)
                    .collect(Collectors.toList());
        }

        public void shutdown() {
            scheduler.shutdown();
        }
    }

    /**
     * 消息存储
     */
    static class MessageStore {
        private final Map<String, Map<String, MessageRecord>> nodeMessages = new ConcurrentHashMap<>();

        public void addMessage(String nodeId, MessageRecord message) {
            nodeMessages.computeIfAbsent(nodeId, k -> new ConcurrentHashMap<>())
                       .put(message.getMessageId(), message);
        }

        public MessageRecord getMessage(String nodeId, String messageId) {
            Map<String, MessageRecord> messages = nodeMessages.get(nodeId);
            return messages != null ? messages.get(messageId) : null;
        }

        public boolean hasMessage(String nodeId, String messageId) {
            Map<String, MessageRecord> messages = nodeMessages.get(nodeId);
            return messages != null && messages.containsKey(messageId);
        }

        public Set<String> getMessageIds(String nodeId) {
            Map<String, MessageRecord> messages = nodeMessages.get(nodeId);
            return messages != null ? new HashSet<>(messages.keySet()) : new HashSet<>();
        }

        public int getMessageCount(String nodeId) {
            Map<String, MessageRecord> messages = nodeMessages.get(nodeId);
            return messages != null ? messages.size() : 0;
        }
    }

    /**
     * 联系人管理器
     */
    static class ContactManager {
        private final Map<String, Set<String>> contactRelations = new ConcurrentHashMap<>();

        public void addContact(String nodeId, String contactId) {
            contactRelations.computeIfAbsent(nodeId, k -> ConcurrentHashMap.newKeySet())
                           .add(contactId);
        }

        public Set<String> getContacts(String nodeId) {
            return new HashSet<>(contactRelations.getOrDefault(nodeId, Collections.emptySet()));
        }

        public boolean areContacts(String nodeId1, String nodeId2) {
            Set<String> contacts1 = contactRelations.get(nodeId1);
            return contacts1 != null && contacts1.contains(nodeId2);
        }
    }

    /**
     * 冲突解决器
     */
    static class ConflictResolver {
        public List<MessageRecord> resolveConflicts(List<MessageRecord> conflictingMessages) {
            return conflictingMessages.stream()
                    .sorted(Comparator.comparing(MessageRecord::getTimestamp)
                            .thenComparing(MessageRecord::getMessageId))
                    .collect(Collectors.toList());
        }

        public MessageRecord resolveMessageConflict(MessageRecord msg1, MessageRecord msg2) {
            // 基于时间戳的冲突解决
            if (msg1.getTimestamp() != msg2.getTimestamp()) {
                return msg1.getTimestamp() < msg2.getTimestamp() ? msg1 : msg2;
            }

            // 时间戳相同时，基于消息ID的字典序
            return msg1.getMessageId().compareTo(msg2.getMessageId()) <= 0 ? msg1 : msg2;
        }
    }

    /**
     * 同步优化器
     */
    static class SyncOptimizer {
        private static final int BLOOM_FILTER_SIZE = 1000;
        private static final int HASH_FUNCTIONS = 3;

        public BloomFilter createBloomFilter(Set<String> items) {
            BloomFilter filter = new BloomFilter(BLOOM_FILTER_SIZE, HASH_FUNCTIONS);
            for (String item : items) {
                filter.add(item);
            }
            return filter;
        }

        public Set<String> filterCandidates(Set<String> candidates, BloomFilter filter) {
            return candidates.stream()
                    .filter(candidate -> !filter.mightContain(candidate))
                    .collect(Collectors.toSet());
        }

        public List<List<String>> createBatches(Set<String> items, int batchSize) {
            List<List<String>> batches = new ArrayList<>();
            List<String> currentBatch = new ArrayList<>();

            for (String item : items) {
                currentBatch.add(item);
                if (currentBatch.size() >= batchSize) {
                    batches.add(new ArrayList<>(currentBatch));
                    currentBatch.clear();
                }
            }

            if (!currentBatch.isEmpty()) {
                batches.add(currentBatch);
            }

            return batches;
        }
    }

    /**
     * 简化版布隆过滤器
     */
    static class BloomFilter {
        private final boolean[] bits;
        private final int hashFunctions;

        public BloomFilter(int size, int hashFunctions) {
            this.bits = new boolean[size];
            this.hashFunctions = hashFunctions;
        }

        public void add(String item) {
            for (int i = 0; i < hashFunctions; i++) {
                int hash = hash(item, i) % bits.length;
                if (hash < 0) hash += bits.length;
                bits[hash] = true;
            }
        }

        public boolean mightContain(String item) {
            for (int i = 0; i < hashFunctions; i++) {
                int hash = hash(item, i) % bits.length;
                if (hash < 0) hash += bits.length;
                if (!bits[hash]) {
                    return false;
                }
            }
            return true;
        }

        private int hash(String item, int seed) {
            // 简单的哈希函数实现
            int hash = seed;
            for (char c : item.toCharArray()) {
                hash = hash * 31 + c;
            }
            return hash;
        }
    }
}
