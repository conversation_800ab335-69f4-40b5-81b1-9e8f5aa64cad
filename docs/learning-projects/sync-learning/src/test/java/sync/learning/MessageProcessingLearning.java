package sync.learning;

import org.junit.Test;
import org.junit.Before;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import static org.junit.Assert.*;

/**
 * 消息处理学习实践
 * 
 * 学习目标：
 * 1. 理解Briar的消息生命周期管理
 * 2. 掌握消息验证和状态转换
 * 3. 学习消息队列和优先级处理
 * 4. 实现离线消息处理机制
 */
public class MessageProcessingLearning {
    
    private MessageProcessor messageProcessor;
    private MessageValidator messageValidator;
    private MessageQueue messageQueue;
    private OfflineMessageManager offlineManager;
    
    @Before
    public void setUp() {
        messageProcessor = new MessageProcessor();
        messageValidator = new MessageValidator();
        messageQueue = new MessageQueue();
        offlineManager = new OfflineMessageManager();
    }
    
    /**
     * 测试1：消息生命周期管理
     */
    @Test
    public void testMessageLifecycle() {
        System.out.println("🔄 开始消息生命周期测试");
        System.out.println("=" .repeat(50));
        
        // 创建测试消息
        Message message = new Message(
            "msg001",
            "group1", 
            "sender1",
            "receiver1",
            System.currentTimeMillis(),
            "Hello, Briar Message Processing!".getBytes(),
            MessageType.PRIVATE_MESSAGE
        );
        
        System.out.println("📝 创建消息:");
        System.out.println("  消息ID: " + message.getId());
        System.out.println("  发送者: " + message.getSenderId());
        System.out.println("  接收者: " + message.getReceiverId());
        System.out.println("  类型: " + message.getType());
        System.out.println("  内容: " + new String(message.getBody()));
        
        // 验证初始状态
        assertEquals("初始状态应该是CREATED", MessageState.CREATED, message.getState());
        
        // 第一步：验证消息
        System.out.println("\n✅ 第一步：消息验证");
        
        ValidationResult validationResult = messageValidator.validate(message);
        assertTrue("消息验证应该通过", validationResult.isValid());
        
        if (validationResult.isValid()) {
            message.setState(MessageState.VALIDATED);
            System.out.println("  ✅ 消息验证通过");
        } else {
            System.out.println("  ❌ 消息验证失败: " + validationResult.getErrorMessage());
        }
        
        // 第二步：加入发送队列
        System.out.println("\n📤 第二步：加入发送队列");
        
        messageQueue.enqueue(message);
        message.setState(MessageState.QUEUED);
        
        System.out.println("  ✅ 消息已加入队列");
        System.out.println("  队列长度: " + messageQueue.size());
        
        // 第三步：发送消息
        System.out.println("\n🚀 第三步：发送消息");
        
        boolean sent = messageProcessor.sendMessage(message);
        if (sent) {
            message.setState(MessageState.SENT);
            System.out.println("  ✅ 消息发送成功");
        } else {
            message.setState(MessageState.FAILED);
            System.out.println("  ❌ 消息发送失败");
        }
        
        // 第四步：确认送达
        System.out.println("\n📨 第四步：确认送达");
        
        // 模拟接收方确认
        boolean delivered = messageProcessor.confirmDelivery(message.getId());
        if (delivered) {
            message.setState(MessageState.DELIVERED);
            System.out.println("  ✅ 消息送达确认");
        }
        
        // 验证最终状态
        assertEquals("最终状态应该是DELIVERED", MessageState.DELIVERED, message.getState());
        
        System.out.println("\n📊 消息生命周期完成:");
        System.out.println("  最终状态: " + message.getState());
        System.out.println("  处理时间: " + (System.currentTimeMillis() - message.getTimestamp()) + "ms");
        
        System.out.println("✅ 消息生命周期测试完成\n");
    }
    
    /**
     * 测试2：消息验证机制
     */
    @Test
    public void testMessageValidation() {
        System.out.println("🔍 开始消息验证机制测试");
        System.out.println("=" .repeat(50));
        
        System.out.println("📋 测试不同的验证场景:");
        
        // 场景1：有效消息
        System.out.println("\n✅ 场景1：有效消息");
        Message validMessage = new Message(
            "valid001", "group1", "sender1", "receiver1",
            System.currentTimeMillis(), "Valid message".getBytes(),
            MessageType.PRIVATE_MESSAGE
        );
        
        ValidationResult result1 = messageValidator.validate(validMessage);
        assertTrue("有效消息应该通过验证", result1.isValid());
        System.out.println("  结果: " + (result1.isValid() ? "通过" : "失败"));
        
        // 场景2：消息体过长
        System.out.println("\n❌ 场景2：消息体过长");
        byte[] longBody = new byte[MessageValidator.MAX_MESSAGE_LENGTH + 1];
        Arrays.fill(longBody, (byte)'A');
        
        Message longMessage = new Message(
            "long001", "group1", "sender1", "receiver1",
            System.currentTimeMillis(), longBody,
            MessageType.PRIVATE_MESSAGE
        );
        
        ValidationResult result2 = messageValidator.validate(longMessage);
        assertFalse("过长消息应该验证失败", result2.isValid());
        System.out.println("  结果: " + (result2.isValid() ? "通过" : "失败"));
        System.out.println("  错误: " + result2.getErrorMessage());
        
        // 场景3：时间戳异常
        System.out.println("\n❌ 场景3：时间戳异常");
        Message futureMessage = new Message(
            "future001", "group1", "sender1", "receiver1",
            System.currentTimeMillis() + 24 * 60 * 60 * 1000,  // 未来24小时
            "Future message".getBytes(),
            MessageType.PRIVATE_MESSAGE
        );
        
        ValidationResult result3 = messageValidator.validate(futureMessage);
        assertFalse("未来时间戳应该验证失败", result3.isValid());
        System.out.println("  结果: " + (result3.isValid() ? "通过" : "失败"));
        System.out.println("  错误: " + result3.getErrorMessage());
        
        // 场景4：空消息体
        System.out.println("\n❌ 场景4：空消息体");
        Message emptyMessage = new Message(
            "empty001", "group1", "sender1", "receiver1",
            System.currentTimeMillis(), new byte[0],
            MessageType.PRIVATE_MESSAGE
        );
        
        ValidationResult result4 = messageValidator.validate(emptyMessage);
        assertFalse("空消息体应该验证失败", result4.isValid());
        System.out.println("  结果: " + (result4.isValid() ? "通过" : "失败"));
        System.out.println("  错误: " + result4.getErrorMessage());
        
        // 场景5：无效的发送者ID
        System.out.println("\n❌ 场景5：无效的发送者ID");
        Message invalidSenderMessage = new Message(
            "invalid001", "group1", "", "receiver1",
            System.currentTimeMillis(), "Invalid sender".getBytes(),
            MessageType.PRIVATE_MESSAGE
        );
        
        ValidationResult result5 = messageValidator.validate(invalidSenderMessage);
        assertFalse("无效发送者应该验证失败", result5.isValid());
        System.out.println("  结果: " + (result5.isValid() ? "通过" : "失败"));
        System.out.println("  错误: " + result5.getErrorMessage());
        
        System.out.println("✅ 消息验证机制测试完成\n");
    }
    
    /**
     * 测试3：消息队列和优先级处理
     */
    @Test
    public void testMessageQueueAndPriority() {
        System.out.println("📋 开始消息队列和优先级测试");
        System.out.println("=" .repeat(50));
        
        // 创建不同优先级的消息
        List<Message> messages = Arrays.asList(
            createMessage("msg001", MessagePriority.LOW, "低优先级消息1"),
            createMessage("msg002", MessagePriority.HIGH, "高优先级消息1"),
            createMessage("msg003", MessagePriority.NORMAL, "普通优先级消息1"),
            createMessage("msg004", MessagePriority.HIGH, "高优先级消息2"),
            createMessage("msg005", MessagePriority.LOW, "低优先级消息2"),
            createMessage("msg006", MessagePriority.URGENT, "紧急消息1"),
            createMessage("msg007", MessagePriority.NORMAL, "普通优先级消息2")
        );
        
        System.out.println("📝 添加消息到队列:");
        for (Message msg : messages) {
            messageQueue.enqueue(msg);
            System.out.println("  " + msg.getId() + ": " + msg.getPriority() + 
                             " - " + new String(msg.getBody()));
        }
        
        System.out.println("\n📊 队列统计:");
        System.out.println("  总消息数: " + messageQueue.size());
        System.out.println("  紧急消息: " + messageQueue.countByPriority(MessagePriority.URGENT));
        System.out.println("  高优先级: " + messageQueue.countByPriority(MessagePriority.HIGH));
        System.out.println("  普通优先级: " + messageQueue.countByPriority(MessagePriority.NORMAL));
        System.out.println("  低优先级: " + messageQueue.countByPriority(MessagePriority.LOW));
        
        // 按优先级处理消息
        System.out.println("\n🚀 按优先级处理消息:");
        
        List<Message> processedMessages = new ArrayList<>();
        while (!messageQueue.isEmpty()) {
            Message msg = messageQueue.dequeue();
            processedMessages.add(msg);
            System.out.println("  处理: " + msg.getId() + " (" + msg.getPriority() + 
                             ") - " + new String(msg.getBody()));
        }
        
        // 验证优先级顺序
        System.out.println("\n🔍 验证优先级顺序:");
        
        for (int i = 1; i < processedMessages.size(); i++) {
            Message prev = processedMessages.get(i - 1);
            Message curr = processedMessages.get(i);
            
            assertTrue("消息应该按优先级排序", 
                      prev.getPriority().getValue() >= curr.getPriority().getValue());
        }
        
        System.out.println("  ✅ 优先级顺序正确");
        
        // 测试批量处理
        System.out.println("\n📦 测试批量处理:");
        
        // 重新添加消息
        for (Message msg : messages) {
            messageQueue.enqueue(msg);
        }
        
        List<Message> batch = messageQueue.dequeueBatch(3);
        assertEquals("批量大小应该正确", 3, batch.size());
        
        System.out.println("  批量处理 " + batch.size() + " 条消息:");
        for (Message msg : batch) {
            System.out.println("    " + msg.getId() + " (" + msg.getPriority() + ")");
        }
        
        System.out.println("✅ 消息队列和优先级测试完成\n");
    }
    
    /**
     * 测试4：离线消息处理
     */
    @Test
    public void testOfflineMessageHandling() {
        System.out.println("📴 开始离线消息处理测试");
        System.out.println("=" .repeat(50));
        
        String senderId = "alice";
        String receiverId = "bob";
        
        // 模拟Bob离线
        offlineManager.setUserOffline(receiverId);
        System.out.println("📱 " + receiverId + " 已离线");
        
        // Alice发送消息给离线的Bob
        System.out.println("\n📤 发送消息给离线用户:");
        
        List<Message> offlineMessages = Arrays.asList(
            createMessage("offline001", MessagePriority.NORMAL, "离线消息1"),
            createMessage("offline002", MessagePriority.HIGH, "重要离线消息"),
            createMessage("offline003", MessagePriority.LOW, "离线消息3")
        );
        
        for (Message msg : offlineMessages) {
            msg.setSenderId(senderId);
            msg.setReceiverId(receiverId);
            
            boolean stored = offlineManager.storeOfflineMessage(msg);
            assertTrue("离线消息应该被存储", stored);
            
            System.out.println("  ✅ 存储: " + msg.getId() + " - " + new String(msg.getBody()));
        }
        
        // 检查离线消息统计
        System.out.println("\n📊 离线消息统计:");
        
        int offlineCount = offlineManager.getOfflineMessageCount(receiverId);
        assertEquals("离线消息数量应该正确", 3, offlineCount);
        System.out.println("  " + receiverId + " 的离线消息: " + offlineCount + " 条");
        
        // 模拟Bob上线
        System.out.println("\n📱 " + receiverId + " 上线");
        offlineManager.setUserOnline(receiverId);
        
        // 获取离线消息
        System.out.println("\n📥 获取离线消息:");
        
        List<Message> retrievedMessages = offlineManager.getOfflineMessages(receiverId);
        assertEquals("应该获取到所有离线消息", 3, retrievedMessages.size());
        
        // 按优先级排序
        retrievedMessages.sort(Comparator.comparing(Message::getPriority, 
                                                   Comparator.reverseOrder()));
        
        for (Message msg : retrievedMessages) {
            System.out.println("  📨 " + msg.getId() + " (" + msg.getPriority() + 
                             ") - " + new String(msg.getBody()));
        }
        
        // 标记消息为已送达
        System.out.println("\n✅ 标记消息为已送达:");
        
        for (Message msg : retrievedMessages) {
            offlineManager.markAsDelivered(msg.getId());
            System.out.println("  ✅ " + msg.getId() + " 已送达");
        }
        
        // 验证离线消息已清理
        int remainingCount = offlineManager.getOfflineMessageCount(receiverId);
        assertEquals("离线消息应该被清理", 0, remainingCount);
        System.out.println("  📊 剩余离线消息: " + remainingCount + " 条");
        
        // 测试离线消息过期
        System.out.println("\n⏰ 测试离线消息过期:");
        
        // 创建过期消息
        System.out.println("\n  创建过期消息:");
        Message expiredMessage = new Message(
            "expired001", "group1", "sender1", "receiver1",
            System.currentTimeMillis() - 8 * 24 * 60 * 60 * 1000, "过期消息".getBytes(),
            MessageType.PRIVATE_MESSAGE
        );
        expiredMessage.setSenderId(senderId);
        expiredMessage.setReceiverId(receiverId);        
        
        offlineManager.setUserOffline(receiverId);
        boolean storedExpired = offlineManager.storeOfflineMessage(expiredMessage);
        assertFalse("过期消息不应该被存储", storedExpired);
        System.out.println("  ❌ 过期消息被拒绝存储");
        
        System.out.println("✅ 离线消息处理测试完成\n");
    }
    
    /**
     * 测试5：消息重传机制
     */
    @Test
    public void testMessageRetransmission() {
        System.out.println("🔄 开始消息重传机制测试");
        System.out.println("=" .repeat(50));
        
        RetransmissionManager retransmissionManager = new RetransmissionManager();
        
        // 创建测试消息
        Message message = createMessage("retry001", MessagePriority.HIGH, "需要重传的消息");
        
        System.out.println("📝 创建消息: " + message.getId());
        System.out.println("  内容: " + new String(message.getBody()));
        
        // 第一次发送失败
        System.out.println("\n❌ 第一次发送失败");
        
        boolean firstAttempt = messageProcessor.sendMessage(message);
        assertFalse("模拟第一次发送失败", firstAttempt);
        
        retransmissionManager.scheduleRetransmission(message);
        System.out.println("  ✅ 已安排重传");
        
        // 检查重传统计
        System.out.println("\n📊 重传统计:");
        
        int pendingRetransmissions = retransmissionManager.getPendingRetransmissionCount();
        assertEquals("应该有1个待重传消息", 1, pendingRetransmissions);
        System.out.println("  待重传消息数: " + pendingRetransmissions);
        
        // 模拟重传过程
        System.out.println("\n🔄 执行重传:");
        
        List<Message> retransmissionQueue = retransmissionManager.getRetransmissionQueue();
        for (Message msg : retransmissionQueue) {
            int attemptCount = retransmissionManager.getAttemptCount(msg.getId());
            System.out.println("  重传 " + msg.getId() + " (第" + (attemptCount + 1) + "次尝试)");
            
            // 模拟重传成功
            boolean retransmissionSuccess = messageProcessor.sendMessage(msg);
            if (retransmissionSuccess) {
                retransmissionManager.markAsSuccessful(msg.getId());
                System.out.println("    ✅ 重传成功");
            } else {
                retransmissionManager.incrementAttemptCount(msg.getId());
                System.out.println("    ❌ 重传失败，将再次重试");
            }
        }
        
        // 验证重传结果
        int finalPendingCount = retransmissionManager.getPendingRetransmissionCount();
        assertEquals("重传成功后应该没有待重传消息", 0, finalPendingCount);
        
        System.out.println("\n📈 重传统计结果:");
        System.out.println("  最终待重传数: " + finalPendingCount);
        System.out.println("  总重传次数: " + retransmissionManager.getTotalRetransmissionCount());
        
        System.out.println("✅ 消息重传机制测试完成\n");
    }
    
    // ========== 辅助方法 ==========
    
    private Message createMessage(String id, MessagePriority priority, String content) {
        Message msg = new Message(
            id, "group1", "sender1", "receiver1",
            System.currentTimeMillis(), content.getBytes(),
            MessageType.PRIVATE_MESSAGE
        );
        msg.setPriority(priority);
        return msg;
    }

    // ========== 消息相关类定义 ==========

    /**
     * 消息类型枚举
     */
    enum MessageType {
        PRIVATE_MESSAGE,
        GROUP_MESSAGE,
        SYSTEM_MESSAGE,
        FILE_ATTACHMENT,
        VOICE_MESSAGE
    }

    /**
     * 消息状态枚举
     */
    enum MessageState {
        CREATED,      // 已创建
        VALIDATED,    // 已验证
        QUEUED,       // 已入队
        SENT,         // 已发送
        DELIVERED,    // 已送达
        READ,         // 已读取
        FAILED,       // 发送失败
        EXPIRED       // 已过期
    }

    /**
     * 消息优先级枚举
     */
    enum MessagePriority {
        LOW(1),
        NORMAL(2),
        HIGH(3),
        URGENT(4);

        private final int value;

        MessagePriority(int value) {
            this.value = value;
        }

        public int getValue() { return value; }
    }

    /**
     * 消息类
     */
    static class Message {
        private final String id;
        private final String groupId;
        private String senderId;
        private String receiverId;
        private final long timestamp;
        private final byte[] body;
        private final MessageType type;
        private MessageState state = MessageState.CREATED;
        private MessagePriority priority = MessagePriority.NORMAL;
        private int retryCount = 0;

        public Message(String id, String groupId, String senderId, String receiverId,
                      long timestamp, byte[] body, MessageType type) {
            this.id = id;
            this.groupId = groupId;
            this.senderId = senderId;
            this.receiverId = receiverId;
            this.timestamp = timestamp;
            this.body = body.clone();
            this.type = type;
        }

        // Getters and Setters
        public String getId() { return id; }
        public String getGroupId() { return groupId; }
        public String getSenderId() { return senderId; }
        public void setSenderId(String senderId) { this.senderId = senderId; }
        public String getReceiverId() { return receiverId; }
        public void setReceiverId(String receiverId) { this.receiverId = receiverId; }
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { /* timestamp is final, this is for testing */ }
        public byte[] getBody() { return body.clone(); }
        public MessageType getType() { return type; }
        public MessageState getState() { return state; }
        public void setState(MessageState state) { this.state = state; }
        public MessagePriority getPriority() { return priority; }
        public void setPriority(MessagePriority priority) { this.priority = priority; }
        public int getRetryCount() { return retryCount; }
        public void incrementRetryCount() { this.retryCount++; }
    }

    /**
     * 验证结果类
     */
    static class ValidationResult {
        private final boolean valid;
        private final String errorMessage;

        public ValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }

        public static ValidationResult success() {
            return new ValidationResult(true, null);
        }

        public static ValidationResult failure(String errorMessage) {
            return new ValidationResult(false, errorMessage);
        }

        public boolean isValid() { return valid; }
        public String getErrorMessage() { return errorMessage; }
    }

    /**
     * 消息验证器
     */
    static class MessageValidator {
        public static final int MAX_MESSAGE_LENGTH = 8192;  // 8KB
        public static final long MAX_CLOCK_SKEW = 5 * 60 * 1000;  // 5分钟

        public ValidationResult validate(Message message) {
            // 检查消息体长度
            if (message.getBody().length == 0) {
                return ValidationResult.failure("消息体不能为空");
            }

            if (message.getBody().length > MAX_MESSAGE_LENGTH) {
                return ValidationResult.failure("消息体长度超过限制: " + message.getBody().length);
            }

            // 检查时间戳
            long now = System.currentTimeMillis();
            if (message.getTimestamp() > now + MAX_CLOCK_SKEW) {
                return ValidationResult.failure("消息时间戳异常：来自未来");
            }

            // 检查发送者ID
            if (message.getSenderId() == null || message.getSenderId().trim().isEmpty()) {
                return ValidationResult.failure("发送者ID不能为空");
            }

            // 检查接收者ID
            if (message.getReceiverId() == null || message.getReceiverId().trim().isEmpty()) {
                return ValidationResult.failure("接收者ID不能为空");
            }

            // 检查消息ID
            if (message.getId() == null || message.getId().trim().isEmpty()) {
                return ValidationResult.failure("消息ID不能为空");
            }

            return ValidationResult.success();
        }
    }

    /**
     * 消息处理器
     */
    static class MessageProcessor {
        private final Map<String, Boolean> deliveryStatus = new ConcurrentHashMap<>();
        private final AtomicInteger sendCounter = new AtomicInteger(0);

        public boolean sendMessage(Message message) {
            // 模拟发送过程
            int sendAttempt = sendCounter.incrementAndGet();

            // 模拟网络不稳定：前几次发送可能失败
            if (sendAttempt % 3 == 1) {  // 每3次中有1次失败
                return false;
            }

            // 模拟发送成功
            try {
                Thread.sleep(10);  // 模拟网络延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            return true;
        }

        public boolean confirmDelivery(String messageId) {
            // 模拟送达确认
            deliveryStatus.put(messageId, true);
            return true;
        }

        public boolean isDelivered(String messageId) {
            return deliveryStatus.getOrDefault(messageId, false);
        }
    }

    /**
     * 消息队列（支持优先级）
     */
    static class MessageQueue {
        private final PriorityQueue<Message> queue = new PriorityQueue<>(
            Comparator.comparing(Message::getPriority, Comparator.reverseOrder())
                     .thenComparing(Message::getTimestamp)
        );

        public void enqueue(Message message) {
            synchronized (queue) {
                queue.offer(message);
            }
        }

        public Message dequeue() {
            synchronized (queue) {
                return queue.poll();
            }
        }

        public List<Message> dequeueBatch(int batchSize) {
            List<Message> batch = new ArrayList<>();
            synchronized (queue) {
                for (int i = 0; i < batchSize && !queue.isEmpty(); i++) {
                    batch.add(queue.poll());
                }
            }
            return batch;
        }

        public int size() {
            synchronized (queue) {
                return queue.size();
            }
        }

        public boolean isEmpty() {
            synchronized (queue) {
                return queue.isEmpty();
            }
        }

        public int countByPriority(MessagePriority priority) {
            synchronized (queue) {
                return (int) queue.stream()
                        .filter(msg -> msg.getPriority() == priority)
                        .count();
            }
        }
    }

    /**
     * 离线消息管理器
     */
    static class OfflineMessageManager {
        private final Map<String, Boolean> userOnlineStatus = new ConcurrentHashMap<>();
        private final Map<String, List<Message>> offlineMessages = new ConcurrentHashMap<>();
        private final Set<String> deliveredMessages = ConcurrentHashMap.newKeySet();

        private static final long OFFLINE_MESSAGE_EXPIRY = 7 * 24 * 60 * 60 * 1000; // 7天

        public void setUserOnline(String userId) {
            userOnlineStatus.put(userId, true);
        }

        public void setUserOffline(String userId) {
            userOnlineStatus.put(userId, false);
        }

        public boolean isUserOnline(String userId) {
            return userOnlineStatus.getOrDefault(userId, false);
        }

        public boolean storeOfflineMessage(Message message) {
            // 检查消息是否过期
            long now = System.currentTimeMillis();
            if (now - message.getTimestamp() > OFFLINE_MESSAGE_EXPIRY) {
                return false;  // 拒绝存储过期消息
            }

            String receiverId = message.getReceiverId();
            if (!isUserOnline(receiverId)) {
                offlineMessages.computeIfAbsent(receiverId, k -> new ArrayList<>())
                              .add(message);
                return true;
            }

            return false;  // 用户在线，不需要存储
        }

        public List<Message> getOfflineMessages(String userId) {
            List<Message> messages = offlineMessages.getOrDefault(userId, new ArrayList<>());

            // 过滤掉已过期的消息
            long now = System.currentTimeMillis();
            return messages.stream()
                    .filter(msg -> now - msg.getTimestamp() <= OFFLINE_MESSAGE_EXPIRY)
                    .collect(Collectors.toList());
        }

        public int getOfflineMessageCount(String userId) {
            return getOfflineMessages(userId).size();
        }

        public void markAsDelivered(String messageId) {
            deliveredMessages.add(messageId);

            // 从离线消息中移除已送达的消息
            for (List<Message> messages : offlineMessages.values()) {
                messages.removeIf(msg -> msg.getId().equals(messageId));
            }
        }

        public boolean isDelivered(String messageId) {
            return deliveredMessages.contains(messageId);
        }

        public void cleanupExpiredMessages() {
            long now = System.currentTimeMillis();

            for (List<Message> messages : offlineMessages.values()) {
                messages.removeIf(msg -> now - msg.getTimestamp() > OFFLINE_MESSAGE_EXPIRY);
            }
        }
    }

    /**
     * 重传管理器
     */
    static class RetransmissionManager {
        private final Map<String, Message> retransmissionQueue = new ConcurrentHashMap<>();
        private final Map<String, Integer> attemptCounts = new ConcurrentHashMap<>();
        private final Set<String> successfulMessages = ConcurrentHashMap.newKeySet();
        private final AtomicInteger totalRetransmissions = new AtomicInteger(0);

        private static final int MAX_RETRY_ATTEMPTS = 3;
        private static final long RETRY_INTERVAL = 5000; // 5秒

        public void scheduleRetransmission(Message message) {
            retransmissionQueue.put(message.getId(), message);
            attemptCounts.put(message.getId(), 0);
        }

        public List<Message> getRetransmissionQueue() {
            return new ArrayList<>(retransmissionQueue.values());
        }

        public int getPendingRetransmissionCount() {
            return retransmissionQueue.size();
        }

        public int getAttemptCount(String messageId) {
            return attemptCounts.getOrDefault(messageId, 0);
        }

        public void incrementAttemptCount(String messageId) {
            int currentCount = attemptCounts.getOrDefault(messageId, 0);
            int newCount = currentCount + 1;

            if (newCount >= MAX_RETRY_ATTEMPTS) {
                // 达到最大重试次数，放弃重传
                retransmissionQueue.remove(messageId);
                attemptCounts.remove(messageId);
            } else {
                attemptCounts.put(messageId, newCount);
            }

            totalRetransmissions.incrementAndGet();
        }

        public void markAsSuccessful(String messageId) {
            retransmissionQueue.remove(messageId);
            attemptCounts.remove(messageId);
            successfulMessages.add(messageId);
        }

        public boolean isSuccessful(String messageId) {
            return successfulMessages.contains(messageId);
        }

        public int getTotalRetransmissionCount() {
            return totalRetransmissions.get();
        }

        public void processRetransmissions(MessageProcessor processor) {
            List<String> toRemove = new ArrayList<>();

            for (Map.Entry<String, Message> entry : retransmissionQueue.entrySet()) {
                String messageId = entry.getKey();
                Message message = entry.getValue();

                int attempts = getAttemptCount(messageId);
                if (attempts < MAX_RETRY_ATTEMPTS) {
                    boolean success = processor.sendMessage(message);
                    if (success) {
                        markAsSuccessful(messageId);
                    } else {
                        incrementAttemptCount(messageId);
                    }
                } else {
                    toRemove.add(messageId);
                }
            }

            // 清理超过最大重试次数的消息
            for (String messageId : toRemove) {
                retransmissionQueue.remove(messageId);
                attemptCounts.remove(messageId);
            }
        }
    }
}
