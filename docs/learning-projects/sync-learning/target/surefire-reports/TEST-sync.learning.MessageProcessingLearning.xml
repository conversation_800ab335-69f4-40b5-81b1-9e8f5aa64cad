<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="sync.learning.MessageProcessingLearning" time="0.035" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/sync-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/sync-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/sync-learning/target/surefire/surefirebooter-20250704161100654_3.jar /Volumes/ExtendData/Code/github/briar/sync-learning/target/surefire 2025-07-04T16-11-00_583-jvmRun1 surefire-20250704161100654_1tmp surefire_0-20250704161100654_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="MessageProcessingLearning"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/sync-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/sync-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/sync-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/sync-learning/target/surefire/surefirebooter-20250704161100654_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/sync-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testOfflineMessageHandling" classname="sync.learning.MessageProcessingLearning" time="0.005">
    <system-out><![CDATA[📴 开始离线消息处理测试
==================================================
📱 bob 已离线

📤 发送消息给离线用户:
  ✅ 存储: offline001 - 离线消息1
  ✅ 存储: offline002 - 重要离线消息
  ✅ 存储: offline003 - 离线消息3

📊 离线消息统计:
  bob 的离线消息: 3 条

📱 bob 上线

📥 获取离线消息:
  📨 offline002 (HIGH) - 重要离线消息
  📨 offline001 (NORMAL) - 离线消息1
  📨 offline003 (LOW) - 离线消息3

✅ 标记消息为已送达:
  ✅ offline002 已送达
  ✅ offline001 已送达
  ✅ offline003 已送达
  📊 剩余离线消息: 0 条

⏰ 测试离线消息过期:

  创建过期消息:
  ❌ 过期消息被拒绝存储
✅ 离线消息处理测试完成

]]></system-out>
  </testcase>
  <testcase name="testMessageRetransmission" classname="sync.learning.MessageProcessingLearning" time="0.012">
    <system-out><![CDATA[🔄 开始消息重传机制测试
==================================================
📝 创建消息: retry001
  内容: 需要重传的消息

❌ 第一次发送失败
  ✅ 已安排重传

📊 重传统计:
  待重传消息数: 1

🔄 执行重传:
  重传 retry001 (第1次尝试)
    ✅ 重传成功

📈 重传统计结果:
  最终待重传数: 0
  总重传次数: 0
✅ 消息重传机制测试完成

]]></system-out>
  </testcase>
  <testcase name="testMessageQueueAndPriority" classname="sync.learning.MessageProcessingLearning" time="0.001">
    <system-out><![CDATA[📋 开始消息队列和优先级测试
==================================================
📝 添加消息到队列:
  msg001: LOW - 低优先级消息1
  msg002: HIGH - 高优先级消息1
  msg003: NORMAL - 普通优先级消息1
  msg004: HIGH - 高优先级消息2
  msg005: LOW - 低优先级消息2
  msg006: URGENT - 紧急消息1
  msg007: NORMAL - 普通优先级消息2

📊 队列统计:
  总消息数: 7
  紧急消息: 1
  高优先级: 2
  普通优先级: 2
  低优先级: 2

🚀 按优先级处理消息:
  处理: msg006 (URGENT) - 紧急消息1
  处理: msg004 (HIGH) - 高优先级消息2
  处理: msg002 (HIGH) - 高优先级消息1
  处理: msg007 (NORMAL) - 普通优先级消息2
  处理: msg003 (NORMAL) - 普通优先级消息1
  处理: msg001 (LOW) - 低优先级消息1
  处理: msg005 (LOW) - 低优先级消息2

🔍 验证优先级顺序:
  ✅ 优先级顺序正确

📦 测试批量处理:
  批量处理 3 条消息:
    msg006 (URGENT)
    msg004 (HIGH)
    msg002 (HIGH)
✅ 消息队列和优先级测试完成

]]></system-out>
  </testcase>
  <testcase name="testMessageValidation" classname="sync.learning.MessageProcessingLearning" time="0.001">
    <system-out><![CDATA[🔍 开始消息验证机制测试
==================================================
📋 测试不同的验证场景:

✅ 场景1：有效消息
  结果: 通过

❌ 场景2：消息体过长
  结果: 失败
  错误: 消息体长度超过限制: 8193

❌ 场景3：时间戳异常
  结果: 失败
  错误: 消息时间戳异常：来自未来

❌ 场景4：空消息体
  结果: 失败
  错误: 消息体不能为空

❌ 场景5：无效的发送者ID
  结果: 失败
  错误: 发送者ID不能为空
✅ 消息验证机制测试完成

]]></system-out>
  </testcase>
  <testcase name="testMessageLifecycle" classname="sync.learning.MessageProcessingLearning" time="0">
    <system-out><![CDATA[🔄 开始消息生命周期测试
==================================================
📝 创建消息:
  消息ID: msg001
  发送者: sender1
  接收者: receiver1
  类型: PRIVATE_MESSAGE
  内容: Hello, Briar Message Processing!

✅ 第一步：消息验证
  ✅ 消息验证通过

📤 第二步：加入发送队列
  ✅ 消息已加入队列
  队列长度: 1

🚀 第三步：发送消息
  ❌ 消息发送失败

📨 第四步：确认送达
  ✅ 消息送达确认

📊 消息生命周期完成:
  最终状态: DELIVERED
  处理时间: 0ms
✅ 消息生命周期测试完成

]]></system-out>
  </testcase>
</testsuite>