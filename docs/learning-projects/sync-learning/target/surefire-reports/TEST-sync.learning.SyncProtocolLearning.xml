<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="sync.learning.SyncProtocolLearning" time="0.036" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/sync-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/sync-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/sync-learning/target/surefire/surefirebooter-20250704143456805_3.jar /Volumes/ExtendData/Code/github/briar/sync-learning/target/surefire 2025-07-04T14-34-56_728-jvmRun1 surefire-20250704143456805_1tmp surefire_0-20250704143456805_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="SyncProtocolLearning"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/sync-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/sync-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.12.0/checker-qual-3.12.0.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/Users/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest/2.1/hamcrest-2.1.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/sync-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/sync-learning/target/surefire/surefirebooter-20250704143456805_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/sync-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testMessageSyncFlow" classname="sync.learning.SyncProtocolLearning" time="0.003">
    <system-out><![CDATA[📡 开始消息同步流程测试
==================================================
📋 初始状态:
  Alice有 3 条消息
  Bob有 2 条消息

📤 第一步：Alice发送Offer
  Alice提供消息: [msg001, msg002, msg003]

📥 第二步：Bob处理Offer并发送Request
  Bob请求消息: [msg001, msg002, msg003]

📦 第三步：Alice发送请求的消息
  发送消息: msg001 (消息1)
  发送消息: msg002 (消息2)
  发送消息: msg003 (消息3)

✅ 第四步：Bob接收消息并发送Ack
  接收消息: msg001 (消息1)
  接收消息: msg002 (消息2)
  接收消息: msg003 (消息3)
  Bob确认消息: [msg001, msg002, msg003]

🔄 第五步：反向同步
  Alice接收: msg004 (消息4)
  Alice接收: msg005 (消息5)

🔍 验证同步结果:
  Alice最终消息数: 5
  Bob最终消息数: 5
✅ 消息同步流程测试完成

]]></system-out>
  </testcase>
  <testcase name="testConflictResolution" classname="sync.learning.SyncProtocolLearning" time="0">
    <system-out><![CDATA[⚔️ 开始冲突解决机制测试
==================================================
📋 冲突消息列表:
  msg001: 第三条消息 (时间: 1751610899913)
  msg002: 第一条消息 (时间: 1751610897913)
  msg003: 第二条消息 (时间: 1751610898913)
  msg004: 同时间消息A (时间: 1751610897913)
  msg005: 同时间消息B (时间: 1751610897913)

🔧 应用冲突解决策略:
📊 解决后的消息顺序:
  1. msg002: 第一条消息 (时间: 1751610897913)
  2. msg004: 同时间消息A (时间: 1751610897913)
  3. msg005: 同时间消息B (时间: 1751610897913)
  4. msg003: 第二条消息 (时间: 1751610898913)
  5. msg001: 第三条消息 (时间: 1751610899913)
✅ 冲突解决机制测试完成

]]></system-out>
  </testcase>
  <testcase name="testIncrementalSync" classname="sync.learning.SyncProtocolLearning" time="0.009">
    <system-out><![CDATA[📈 开始增量同步优化测试
==================================================
📊 创建大量消息进行测试:
  Alice有消息: 1-80 (共80条)
  Bob有消息: 21-100 (共80条)
  重叠消息: 21-80 (共60条)

🔍 计算增量同步:
  Alice需要发送给Bob: 20 条
  Bob需要发送给Alice: 20 条

⚡ 执行优化同步:
  布隆过滤器候选 - Alice: 20
  布隆过滤器候选 - Bob: 20
  Alice需要发送批次: 2
  Bob需要发送批次: 2

📊 同步效率统计:
  总消息数: 160
  唯一消息数: 100
  重复消息数: 60
  压缩比: 37.50%
✅ 增量同步优化测试完成

]]></system-out>
  </testcase>
  <testcase name="testSyncSessionEstablishment" classname="sync.learning.SyncProtocolLearning" time="0.006">
    <system-out><![CDATA[🤝 开始同步会话建立测试
==================================================
👥 模拟Alice和Bob的同步会话:
  📤 第一步：版本协商
     Alice支持版本: [0, 1]
     Bob支持版本: [0, 1]
     ✅ 版本协商成功
  🎲 第二步：优先级交换
     Alice优先级: f56e9b4e411f45a4...
     Bob优先级: 19f2df71625c1524...
     ✅ 主导方: Alice
  🎉 会话建立完成
     Alice状态: ESTABLISHED
     Bob状态: ESTABLISHED
✅ 同步会话建立测试完成

]]></system-out>
  </testcase>
  <testcase name="testSyncRecordTypes" classname="sync.learning.SyncProtocolLearning" time="0.001">
    <system-out><![CDATA[📦 开始同步记录类型测试
==================================================
🔧 创建不同类型的同步记录:
  ✅ 版本记录: 10 字节
     支持版本: [0, 1]
  ✅ 优先级记录: 20 字节
     随机数: 0748a81ce282d5ba...
  ✅ Offer记录: 38 字节
     消息数量: 3
  ✅ Request记录: 28 字节
     请求数量: 2
  ✅ Message记录: 54 字节
     消息ID: msg001
     消息长度: 18 字节
  ✅ Ack记录: 28 字节
     确认数量: 2

🔍 测试记录的序列化和反序列化:
  ✅ 版本记录序列化/反序列化成功
  ✅ Offer记录序列化/反序列化成功
  ✅ Message记录序列化/反序列化成功
✅ 同步记录类型测试完成

]]></system-out>
  </testcase>
</testsuite>