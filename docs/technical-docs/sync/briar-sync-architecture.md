# Briar同步机制深度解析

## 🔄 同步系统概述

Briar的同步机制是一个精心设计的分布式数据一致性系统，它解决了去中心化环境下的消息同步、冲突解决、离线处理等核心挑战。该系统通过创新的协议设计和算法实现，确保了数据的最终一致性和系统的高可用性。

## 📋 同步协议设计

### 协议版本管理

```java
public interface SyncConstants {
    byte PROTOCOL_VERSION = 0;
    List<Byte> SUPPORTED_VERSIONS = singletonList(PROTOCOL_VERSION);
    int MAX_SUPPORTED_VERSIONS = 10;
}

public class VersionNegotiation {
    public byte negotiateVersion(List<Byte> localVersions, List<Byte> remoteVersions) {
        // 找到双方都支持的最高版本
        return localVersions.stream()
            .filter(remoteVersions::contains)
            .max(Byte::compareTo)
            .orElseThrow(() -> new IncompatibleVersionException());
    }
}
```

### 核心记录类型

```java
// 版本协商记录
public class Versions {
    private final List<Byte> supportedVersions;
    
    public Versions(List<Byte> supportedVersions) {
        this.supportedVersions = supportedVersions;
    }
}

// 消息提供记录
public class Offer {
    private final List<MessageId> messageIds;
    
    public Offer(List<MessageId> messageIds) {
        this.messageIds = messageIds;
    }
}

// 消息请求记录
public class Request {
    private final List<MessageId> messageIds;
    
    public Request(List<MessageId> messageIds) {
        this.messageIds = messageIds;
    }
}

// 消息确认记录
public class Ack {
    private final List<MessageId> messageIds;
    
    public Ack(List<MessageId> messageIds) {
        this.messageIds = messageIds;
    }
}

// 优先级记录
public class Priority {
    private final byte[] nonce;
    
    public Priority(byte[] nonce) {
        this.nonce = nonce;
    }
}
```

### 记录序列化格式

```java
public class SyncRecordWriter {
    private static final byte VERSIONS = 0;
    private static final byte MESSAGE = 1;
    private static final byte OFFER = 2;
    private static final byte REQUEST = 3;
    private static final byte ACK = 4;
    private static final byte PRIORITY = 5;
    
    public void writeVersions(Versions v) throws IOException {
        ByteArrayOutputStream payload = new ByteArrayOutputStream();
        for (byte version : v.getSupportedVersions()) {
            payload.write(version);
        }
        writeRecord(VERSIONS, payload.toByteArray());
    }
    
    public void writeOffer(Offer o) throws IOException {
        ByteArrayOutputStream payload = new ByteArrayOutputStream();
        for (MessageId id : o.getMessageIds()) {
            payload.write(id.getBytes());
        }
        writeRecord(OFFER, payload.toByteArray());
    }
    
    private void writeRecord(byte type, byte[] payload) throws IOException {
        // 记录格式: [版本][类型][长度][载荷]
        writer.write(PROTOCOL_VERSION);
        writer.write(type);
        writer.writeInt(payload.length);
        writer.write(payload);
    }
}
```

## 🔄 同步会话管理

### 会话类型和生命周期

```java
public interface SyncSession {
    void run() throws IOException;
    void interrupt();
}

// 双工输出会话
public class DuplexOutgoingSession implements SyncSession {
    @Override
    public void run() throws IOException {
        // 1. 发送支持的协议版本
        recordWriter.writeVersions(new Versions(SUPPORTED_VERSIONS));
        
        // 2. 发送连接优先级（如果是主动连接）
        if (priority != null) {
            recordWriter.writePriority(priority);
        }
        
        // 3. 开始各种记录的查询生成
        generateAck();
        generateBatch();
        generateOffer();
        generateRequest();
        
        // 4. 主循环：写入记录直到中断
        long nextKeepalive = clock.currentTimeMillis() + maxIdleTime;
        while (!interrupted) {
            long now = clock.currentTimeMillis();
            long keepaliveWait = Math.max(0, nextKeepalive - now);
            
            Record record = recordQueue.poll(keepaliveWait, MILLISECONDS);
            if (record == null) {
                // 发送保活记录
                sendKeepalive();
                nextKeepalive = clock.currentTimeMillis() + maxIdleTime;
            } else {
                recordWriter.writeRecord(record);
                if (record.isMessage()) {
                    nextKeepalive = clock.currentTimeMillis() + maxIdleTime;
                }
            }
        }
    }
}

// 单工输出会话
public class SimplexOutgoingSession implements SyncSession {
    @Override
    public void run() throws IOException {
        // 1. 发送协议版本
        recordWriter.writeVersions(new Versions(SUPPORTED_VERSIONS));
        
        // 2. 发送确认和消息
        sendAcks();
        sendMessages();
        
        // 3. 发送流结束标记
        streamWriter.sendEndOfStream();
    }
}
```

### 会话工厂

```java
public class SyncSessionFactory {
    public SyncSession createIncomingSession(ContactId contactId, InputStream in) {
        SyncRecordReader recordReader = recordReaderFactory.createRecordReader(in);
        return new IncomingSession(db, eventBus, contactId, recordReader);
    }
    
    public SyncSession createDuplexOutgoingSession(ContactId contactId, 
            TransportId transportId, long maxLatency, 
            DuplexTransportConnection connection) {
        OutputStream out = connection.getOutputStream();
        SyncRecordWriter recordWriter = recordWriterFactory.createRecordWriter(out);
        return new DuplexOutgoingSession(db, eventBus, contactId, transportId,
                maxLatency, connection, recordWriter);
    }
    
    public SyncSession createSimplexOutgoingSession(ContactId contactId,
            TransportId transportId, long maxLatency, boolean eager,
            StreamWriter streamWriter) {
        OutputStream out = streamWriter.getOutputStream();
        SyncRecordWriter recordWriter = recordWriterFactory.createRecordWriter(out);
        
        if (eager) {
            return new EagerSimplexOutgoingSession(db, eventBus, contactId,
                    transportId, maxLatency, streamWriter, recordWriter);
        } else {
            return new SimplexOutgoingSession(db, eventBus, contactId,
                    transportId, maxLatency, streamWriter, recordWriter);
        }
    }
}
```

## 📨 消息同步算法

### 标准同步流程

```java
public class MessageSyncFlow {
    // 第一阶段：消息发现
    public void discoverMessages(ContactId contactId) throws DbException {
        // 1. 获取本地可发送的消息
        Collection<MessageId> localMessages = db.getMessagesToOffer(contactId);
        
        // 2. 发送Offer记录
        if (!localMessages.isEmpty()) {
            Offer offer = new Offer(new ArrayList<>(localMessages));
            recordWriter.writeOffer(offer);
        }
    }
    
    // 第二阶段：消息请求
    public void requestMessages(Offer offer, ContactId contactId) throws DbException {
        // 1. 过滤出需要的消息
        List<MessageId> neededMessages = offer.getMessageIds().stream()
            .filter(id -> !db.containsMessage(id))
            .filter(id -> db.shouldRequestMessage(contactId, id))
            .collect(Collectors.toList());
        
        // 2. 发送Request记录
        if (!neededMessages.isEmpty()) {
            Request request = new Request(neededMessages);
            recordWriter.writeRequest(request);
        }
    }
    
    // 第三阶段：消息传输
    public void sendMessages(Request request, ContactId contactId) throws DbException {
        for (MessageId messageId : request.getMessageIds()) {
            if (db.containsMessage(messageId)) {
                Message message = db.getMessage(messageId);
                if (db.shouldSendMessage(contactId, message)) {
                    recordWriter.writeMessage(message);
                }
            }
        }
    }
    
    // 第四阶段：确认接收
    public void acknowledgeMessages(List<MessageId> receivedMessages) throws IOException {
        if (!receivedMessages.isEmpty()) {
            Ack ack = new Ack(receivedMessages);
            recordWriter.writeAck(ack);
        }
    }
}
```

### 增量同步优化

```java
public class IncrementalSync {
    private final Map<ContactId, SyncState> syncStates = new ConcurrentHashMap<>();
    
    public void performIncrementalSync(ContactId contactId) throws DbException {
        SyncState state = syncStates.get(contactId);
        if (state == null) {
            // 首次同步，执行完整同步
            performFullSync(contactId);
            return;
        }
        
        // 获取自上次同步以来的新消息
        long lastSyncTime = state.getLastSyncTime();
        Collection<MessageId> newMessages = db.getMessagesSince(contactId, lastSyncTime);
        
        if (!newMessages.isEmpty()) {
            // 发送增量Offer
            Offer incrementalOffer = new Offer(new ArrayList<>(newMessages));
            recordWriter.writeOffer(incrementalOffer);
            
            // 更新同步状态
            state.setLastSyncTime(clock.currentTimeMillis());
        }
    }
    
    private void performFullSync(ContactId contactId) throws DbException {
        Collection<MessageId> allMessages = db.getMessagesToOffer(contactId);
        if (!allMessages.isEmpty()) {
            Offer fullOffer = new Offer(new ArrayList<>(allMessages));
            recordWriter.writeOffer(fullOffer);
        }
        
        // 创建新的同步状态
        SyncState newState = new SyncState(contactId, clock.currentTimeMillis());
        syncStates.put(contactId, newState);
    }
}
```

## ⚔️ 冲突检测和解决

### 冲突检测机制

```java
public class ConflictDetector {
    public List<ConflictGroup> detectConflicts(List<Message> messages) {
        Map<String, List<Message>> groupedByKey = messages.stream()
            .collect(Collectors.groupingBy(this::getConflictKey));
        
        return groupedByKey.values().stream()
            .filter(group -> group.size() > 1)
            .map(ConflictGroup::new)
            .collect(Collectors.toList());
    }
    
    private String getConflictKey(Message message) {
        // 基于消息的关键属性生成冲突键
        return message.getGroupId() + ":" + message.getTimestamp();
    }
    
    public ConflictType classifyConflict(ConflictGroup conflict) {
        List<Message> messages = conflict.getMessages();
        
        // 检查时间戳冲突
        if (hasTimestampConflict(messages)) {
            return ConflictType.TIMESTAMP_CONFLICT;
        }
        
        // 检查内容冲突
        if (hasContentConflict(messages)) {
            return ConflictType.CONTENT_CONFLICT;
        }
        
        // 检查顺序冲突
        if (hasOrderConflict(messages)) {
            return ConflictType.ORDER_CONFLICT;
        }
        
        return ConflictType.NO_CONFLICT;
    }
}
```

### 向量时钟实现

```java
public class VectorClock {
    private final Map<String, Integer> clock;
    
    public VectorClock() {
        this.clock = new ConcurrentHashMap<>();
    }
    
    public VectorClock(Map<String, Integer> clock) {
        this.clock = new ConcurrentHashMap<>(clock);
    }
    
    public void tick(String nodeId) {
        clock.merge(nodeId, 1, Integer::sum);
    }
    
    public void update(VectorClock other) {
        for (Map.Entry<String, Integer> entry : other.clock.entrySet()) {
            String nodeId = entry.getKey();
            int otherValue = entry.getValue();
            clock.merge(nodeId, otherValue, Integer::max);
        }
    }
    
    public Ordering compare(VectorClock other) {
        Set<String> allNodes = new HashSet<>(clock.keySet());
        allNodes.addAll(other.clock.keySet());
        
        boolean thisGreater = false;
        boolean otherGreater = false;
        
        for (String nodeId : allNodes) {
            int thisValue = clock.getOrDefault(nodeId, 0);
            int otherValue = other.clock.getOrDefault(nodeId, 0);
            
            if (thisValue > otherValue) {
                thisGreater = true;
            } else if (otherValue > thisValue) {
                otherGreater = true;
            }
        }
        
        if (thisGreater && !otherGreater) {
            return Ordering.AFTER;
        } else if (otherGreater && !thisGreater) {
            return Ordering.BEFORE;
        } else if (!thisGreater && !otherGreater) {
            return Ordering.EQUAL;
        } else {
            return Ordering.CONCURRENT;
        }
    }
}

public enum Ordering {
    BEFORE, AFTER, EQUAL, CONCURRENT
}
```

### 冲突解决策略

```java
public class ConflictResolver {
    // 基于时间戳的解决策略
    public Message resolveByTimestamp(ConflictGroup conflict) {
        return conflict.getMessages().stream()
            .min(Comparator.comparing(Message::getTimestamp))
            .orElseThrow();
    }
    
    // 基于发送者优先级的解决策略
    public Message resolveBySenderPriority(ConflictGroup conflict) {
        return conflict.getMessages().stream()
            .min(Comparator.comparing(msg -> getSenderPriority(msg.getSenderId())))
            .orElseThrow();
    }
    
    // 基于向量时钟的解决策略
    public Message resolveByVectorClock(ConflictGroup conflict) {
        return conflict.getMessages().stream()
            .min((m1, m2) -> {
                VectorClock c1 = m1.getVectorClock();
                VectorClock c2 = m2.getVectorClock();
                Ordering ordering = c1.compare(c2);
                
                switch (ordering) {
                    case BEFORE: return -1;
                    case AFTER: return 1;
                    case EQUAL: return 0;
                    case CONCURRENT: 
                        // 并发事件，使用消息ID作为决定因子
                        return m1.getId().compareTo(m2.getId());
                    default: return 0;
                }
            })
            .orElseThrow();
    }
    
    // 基于内容哈希的确定性解决策略
    public Message resolveByContentHash(ConflictGroup conflict) {
        return conflict.getMessages().stream()
            .min(Comparator.comparing(msg -> Arrays.hashCode(msg.getBody())))
            .orElseThrow();
    }
}
```

## 📱 离线同步处理

### 离线消息存储

```java
public class OfflineMessageStore {
    private final Map<ContactId, Queue<OfflineMessage>> offlineMessages;
    private final long maxOfflineTime;
    
    public void storeOfflineMessage(ContactId contactId, Message message, 
            MessagePriority priority) {
        OfflineMessage offlineMsg = new OfflineMessage(
            message, 
            clock.currentTimeMillis(),
            priority
        );
        
        Queue<OfflineMessage> queue = offlineMessages.computeIfAbsent(
            contactId, 
            k -> new PriorityQueue<>(Comparator.comparing(OfflineMessage::getPriority)
                                              .thenComparing(OfflineMessage::getTimestamp))
        );
        
        queue.offer(offlineMsg);
        
        // 清理过期消息
        cleanupExpiredMessages(contactId);
    }
    
    public List<Message> retrieveOfflineMessages(ContactId contactId) {
        Queue<OfflineMessage> queue = offlineMessages.get(contactId);
        if (queue == null) {
            return Collections.emptyList();
        }
        
        List<Message> messages = new ArrayList<>();
        OfflineMessage offlineMsg;
        while ((offlineMsg = queue.poll()) != null) {
            if (!isExpired(offlineMsg)) {
                messages.add(offlineMsg.getMessage());
            }
        }
        
        return messages;
    }
    
    private void cleanupExpiredMessages(ContactId contactId) {
        Queue<OfflineMessage> queue = offlineMessages.get(contactId);
        if (queue != null) {
            queue.removeIf(this::isExpired);
        }
    }
    
    private boolean isExpired(OfflineMessage offlineMsg) {
        long age = clock.currentTimeMillis() - offlineMsg.getTimestamp();
        return age > maxOfflineTime;
    }
}
```

### 网络恢复同步

```java
public class NetworkRecoverySync {
    public void performRecoverySync(ContactId contactId) throws DbException {
        LOG.info("Starting recovery sync for contact: " + contactId);
        
        // 1. 检查离线期间的消息
        List<Message> offlineMessages = offlineMessageStore.retrieveOfflineMessages(contactId);
        
        // 2. 获取本地状态
        SyncState localState = syncStateManager.getState(contactId);
        
        // 3. 执行增量同步
        if (localState != null) {
            performIncrementalRecoverySync(contactId, localState, offlineMessages);
        } else {
            performFullRecoverySync(contactId, offlineMessages);
        }
        
        // 4. 更新同步状态
        syncStateManager.updateState(contactId, clock.currentTimeMillis());
        
        LOG.info("Recovery sync completed for contact: " + contactId);
    }
    
    private void performIncrementalRecoverySync(ContactId contactId, 
            SyncState localState, List<Message> offlineMessages) throws DbException {
        
        // 发送离线期间收到的消息
        if (!offlineMessages.isEmpty()) {
            List<MessageId> offlineMessageIds = offlineMessages.stream()
                .map(Message::getId)
                .collect(Collectors.toList());
            
            Offer offlineOffer = new Offer(offlineMessageIds);
            recordWriter.writeOffer(offlineOffer);
        }
        
        // 请求自上次同步以来的消息
        long lastSyncTime = localState.getLastSyncTime();
        Collection<MessageId> missedMessages = db.getMessagesSince(contactId, lastSyncTime);
        
        if (!missedMessages.isEmpty()) {
            Request missedRequest = new Request(new ArrayList<>(missedMessages));
            recordWriter.writeRequest(missedRequest);
        }
    }
}
```

## 📊 性能优化策略

### 批量处理优化

```java
public class BatchSyncOptimizer {
    private static final int MAX_BATCH_SIZE = 100;
    private static final long BATCH_TIMEOUT_MS = 5000;
    
    public void optimizeBatchSync(ContactId contactId) throws DbException {
        List<MessageId> pendingOffers = new ArrayList<>();
        long batchStartTime = clock.currentTimeMillis();
        
        while (true) {
            // 收集待发送的消息
            Collection<MessageId> newMessages = db.getMessagesToOffer(contactId);
            pendingOffers.addAll(newMessages);
            
            // 检查批量条件
            boolean sizeLimitReached = pendingOffers.size() >= MAX_BATCH_SIZE;
            boolean timeoutReached = (clock.currentTimeMillis() - batchStartTime) >= BATCH_TIMEOUT_MS;
            boolean noMoreMessages = newMessages.isEmpty();
            
            if (sizeLimitReached || timeoutReached || noMoreMessages) {
                // 发送批量Offer
                if (!pendingOffers.isEmpty()) {
                    Offer batchOffer = new Offer(new ArrayList<>(pendingOffers));
                    recordWriter.writeOffer(batchOffer);
                    pendingOffers.clear();
                }
                
                if (noMoreMessages) {
                    break;
                }
                
                batchStartTime = clock.currentTimeMillis();
            }
            
            // 短暂等待以收集更多消息
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
}
```

---

**Briar的同步机制体现了分布式系统设计的最高水准** 🔄

通过精心设计的协议、智能的冲突解决、高效的离线处理和性能优化，Briar构建了一个强大、可靠、高效的分布式同步系统，为去中心化通信提供了坚实的数据一致性保障。
