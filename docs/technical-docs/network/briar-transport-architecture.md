# Briar传输架构深度解析

## 🌐 传输系统概述

Briar采用插件化的传输架构，支持多种传输协议的并行使用，实现了灵活、可扩展、高可靠的分布式通信系统。这种设计使得Briar能够在各种网络环境下保持通信能力。

## 🏗️ 插件化架构设计

### 核心接口层次

```java
// 基础插件接口
public interface Plugin {
    TransportId getId();
    PluginState getState();
    void start();
    void stop();
    long getMaxLatency();
    int getMaxIdleTime();
}

// 双工传输插件
public interface DuplexPlugin extends Plugin {
    DuplexTransportConnection createConnection(TransportProperties properties);
    boolean supportsKeyAgreement();
}

// 单工传输插件  
public interface SimplexPlugin extends Plugin {
    boolean isLossyAndCheap();
    TransportConnectionReader createReader(TransportProperties properties);
    TransportConnectionWriter createWriter(TransportProperties properties);
}
```

### 插件生命周期管理

```java
public enum PluginState {
    INACTIVE,    // 未激活状态
    STARTING,    // 启动中状态
    ACTIVE,      // 活跃状态
    DISABLED     // 禁用状态
}

public class PluginManager {
    private final Map<TransportId, Plugin> plugins = new ConcurrentHashMap<>();
    private final Map<TransportId, CountDownLatch> startLatches = new ConcurrentHashMap<>();
    
    public void startPlugin(Plugin plugin) {
        plugin.setState(PluginState.STARTING);
        CountDownLatch latch = new CountDownLatch(1);
        startLatches.put(plugin.getId(), latch);
        
        executor.execute(() -> {
            try {
                plugin.start();
                plugin.setState(PluginState.ACTIVE);
            } catch (Exception e) {
                plugin.setState(PluginState.DISABLED);
                LOG.warning("Failed to start plugin: " + plugin.getId());
            } finally {
                latch.countDown();
            }
        });
    }
}
```

## 🔌 传输协议实现

### 1. Tor传输插件

```java
public class TorPlugin implements DuplexPlugin {
    private static final int DEFAULT_SOCKS_PORT = 9050;
    private static final String ONION_SUFFIX = ".onion";
    
    @Override
    public DuplexTransportConnection createConnection(TransportProperties properties) {
        String address = properties.get("address");
        int port = Integer.parseInt(properties.get("port"));
        
        if (address.endsWith(ONION_SUFFIX)) {
            // 创建Tor隐藏服务连接
            return createOnionConnection(address, port);
        } else {
            // 创建Tor代理连接
            return createProxyConnection(address, port);
        }
    }
    
    private DuplexTransportConnection createOnionConnection(String onionAddress, int port) {
        try {
            // 通过Tor SOCKS代理连接隐藏服务
            Socket socket = new Socket();
            socket.connect(new InetSocketAddress("127.0.0.1", DEFAULT_SOCKS_PORT));
            
            // SOCKS5握手和连接建立
            performSocksHandshake(socket, onionAddress, port);
            
            return new SocketTransportConnection(socket);
        } catch (IOException e) {
            LOG.warning("Failed to create Tor connection: " + e.getMessage());
            return null;
        }
    }
    
    @Override
    public boolean supportsKeyAgreement() {
        return true; // Tor支持密钥协商
    }
    
    @Override
    public long getMaxLatency() {
        return 30000; // Tor网络延迟较高
    }
}
```

### 2. TCP传输插件

```java
public class TcpPlugin implements DuplexPlugin {
    private final int listenPort;
    private ServerSocket serverSocket;
    
    @Override
    public DuplexTransportConnection createConnection(TransportProperties properties) {
        String address = properties.get("address");
        int port = Integer.parseInt(properties.get("port"));
        
        try {
            Socket socket = new Socket();
            socket.setTcpNoDelay(true);
            socket.setSoTimeout(30000);
            socket.connect(new InetSocketAddress(address, port), 10000);
            
            return new SocketTransportConnection(socket);
        } catch (IOException e) {
            LOG.warning("Failed to create TCP connection: " + e.getMessage());
            return null;
        }
    }
    
    @Override
    public void start() {
        try {
            serverSocket = new ServerSocket(listenPort);
            setState(PluginState.ACTIVE);
            
            // 启动连接接受线程
            executor.execute(this::acceptConnections);
        } catch (IOException e) {
            setState(PluginState.DISABLED);
            throw new RuntimeException("Failed to start TCP plugin", e);
        }
    }
    
    private void acceptConnections() {
        while (getState() == PluginState.ACTIVE) {
            try {
                Socket socket = serverSocket.accept();
                handleIncomingConnection(socket);
            } catch (IOException e) {
                if (getState() == PluginState.ACTIVE) {
                    LOG.warning("Error accepting connection: " + e.getMessage());
                }
            }
        }
    }
    
    @Override
    public long getMaxLatency() {
        return 5000; // TCP延迟较低
    }
}
```

### 3. 蓝牙传输插件

```java
public class BluetoothPlugin implements DuplexPlugin {
    private static final UUID SERVICE_UUID = UUID.fromString("...");
    private BluetoothAdapter adapter;
    private BluetoothServerSocket serverSocket;
    
    @Override
    public DuplexTransportConnection createConnection(TransportProperties properties) {
        String deviceAddress = properties.get("deviceAddress");
        
        try {
            BluetoothDevice device = adapter.getRemoteDevice(deviceAddress);
            BluetoothSocket socket = device.createRfcommSocketToServiceRecord(SERVICE_UUID);
            socket.connect();
            
            return new BluetoothTransportConnection(socket);
        } catch (IOException e) {
            LOG.warning("Failed to create Bluetooth connection: " + e.getMessage());
            return null;
        }
    }
    
    @Override
    public boolean supportsKeyAgreement() {
        return true; // 蓝牙支持配对和密钥协商
    }
    
    @Override
    public long getMaxLatency() {
        return 10000; // 蓝牙延迟中等
    }
}
```

### 4. 文件传输插件

```java
public class FilePlugin implements SimplexPlugin {
    private final File transferDirectory;
    
    @Override
    public TransportConnectionReader createReader(TransportProperties properties) {
        String filePath = properties.get("path");
        if (filePath == null) return null;
        
        try {
            File file = new File(transferDirectory, filePath);
            if (!file.exists()) return null;
            
            FileInputStream inputStream = new FileInputStream(file);
            return new TransportInputStreamReader(inputStream);
        } catch (IOException e) {
            LOG.warning("Failed to create file reader: " + e.getMessage());
            return null;
        }
    }
    
    @Override
    public TransportConnectionWriter createWriter(TransportProperties properties) {
        String filePath = properties.get("path");
        if (filePath == null) return null;
        
        try {
            File file = new File(transferDirectory, filePath);
            file.getParentFile().mkdirs();
            
            FileOutputStream outputStream = new FileOutputStream(file);
            return new TransportOutputStreamWriter(outputStream);
        } catch (IOException e) {
            LOG.warning("Failed to create file writer: " + e.getMessage());
            return null;
        }
    }
    
    @Override
    public boolean isLossyAndCheap() {
        return false; // 文件传输是可靠的
    }
    
    @Override
    public long getMaxLatency() {
        return Long.MAX_VALUE; // 文件传输可能需要很长时间
    }
}
```

## 📦 网络协议设计

### 协议帧结构

```java
public abstract class ProtocolFrame {
    protected static final int FRAME_HEADER_LENGTH = 4;
    protected static final int MAC_LENGTH = 16;
    
    protected final FrameType type;
    protected final byte[] data;
    
    public byte[] serialize() {
        ByteBuffer buffer = ByteBuffer.allocate(getFrameLength());
        
        // 写入帧头
        buffer.putInt(type.getValue());
        
        // 写入帧数据
        writeFrameData(buffer);
        
        // 计算和写入MAC
        byte[] mac = calculateMAC(buffer.array(), 0, buffer.position());
        buffer.put(mac);
        
        return buffer.array();
    }
    
    protected abstract void writeFrameData(ByteBuffer buffer);
    protected abstract int getFrameLength();
}

public enum FrameType {
    HANDSHAKE(0x01),
    DATA(0x02),
    ACK(0x03),
    HEARTBEAT(0x04);
    
    private final int value;
    
    FrameType(int value) {
        this.value = value;
    }
    
    public int getValue() {
        return value;
    }
}
```

### 握手协议实现

```java
public class HandshakeFrame extends ProtocolFrame {
    private final ProtocolVersion version;
    private final byte[] publicKey;
    private final long timestamp;
    
    public HandshakeFrame(ProtocolVersion version, byte[] publicKey, long timestamp) {
        super(FrameType.HANDSHAKE);
        this.version = version;
        this.publicKey = publicKey;
        this.timestamp = timestamp;
    }
    
    @Override
    protected void writeFrameData(ByteBuffer buffer) {
        buffer.putInt(version.getValue());
        buffer.putInt(publicKey.length);
        buffer.put(publicKey);
        buffer.putLong(timestamp);
    }
    
    public static HandshakeFrame deserialize(byte[] data) {
        ByteBuffer buffer = ByteBuffer.wrap(data);
        
        // 跳过帧头
        buffer.getInt();
        
        // 读取握手数据
        ProtocolVersion version = ProtocolVersion.fromValue(buffer.getInt());
        int keyLength = buffer.getInt();
        byte[] publicKey = new byte[keyLength];
        buffer.get(publicKey);
        long timestamp = buffer.getLong();
        
        return new HandshakeFrame(version, publicKey, timestamp);
    }
}
```

### 可靠数据传输

```java
public class DataFrame extends ProtocolFrame {
    private final long sequenceNumber;
    private final byte[] payload;
    private final boolean isLastFrame;
    
    public DataFrame(long sequenceNumber, byte[] payload, boolean isLastFrame) {
        super(FrameType.DATA);
        this.sequenceNumber = sequenceNumber;
        this.payload = payload;
        this.isLastFrame = isLastFrame;
    }
    
    @Override
    protected void writeFrameData(ByteBuffer buffer) {
        buffer.putLong(sequenceNumber);
        buffer.put(isLastFrame ? (byte) 1 : (byte) 0);
        buffer.putInt(payload.length);
        buffer.put(payload);
    }
}

public class ReliableTransport {
    private final Map<Long, DataFrame> unacknowledgedFrames = new ConcurrentHashMap<>();
    private final ScheduledExecutorService retransmissionExecutor;
    private long nextSequenceNumber = 1;
    
    public void sendData(byte[] data) {
        DataFrame frame = new DataFrame(nextSequenceNumber++, data, false);
        unacknowledgedFrames.put(frame.getSequenceNumber(), frame);
        
        sendFrame(frame);
        scheduleRetransmission(frame);
    }
    
    private void scheduleRetransmission(DataFrame frame) {
        retransmissionExecutor.schedule(() -> {
            if (unacknowledgedFrames.containsKey(frame.getSequenceNumber())) {
                LOG.info("Retransmitting frame: " + frame.getSequenceNumber());
                sendFrame(frame);
                scheduleRetransmission(frame);
            }
        }, 5, TimeUnit.SECONDS);
    }
    
    public void processAck(AckFrame ack) {
        DataFrame frame = unacknowledgedFrames.remove(ack.getSequenceNumber());
        if (frame != null) {
            LOG.info("Received ACK for frame: " + ack.getSequenceNumber());
        }
    }
}
```

## 🎯 传输选择策略

### 智能传输选择

```java
public class TransportSelector {
    private final Map<String, Integer> transportPriorities = new HashMap<>();
    
    public TransportSelector() {
        // 设置默认优先级
        transportPriorities.put("TOR", 1);        // 匿名性优先
        transportPriorities.put("TCP", 2);        // 性能优先
        transportPriorities.put("BLUETOOTH", 3);  // 近距离通信
        transportPriorities.put("FILE", 4);       // 离线传输
    }
    
    public Plugin selectBestTransport(List<Plugin> availablePlugins, TransportCriteria criteria) {
        return availablePlugins.stream()
            .filter(p -> p.getState() == PluginState.ACTIVE)
            .filter(p -> meetsCriteria(p, criteria))
            .min(Comparator.comparing(this::getEffectivePriority))
            .orElse(null);
    }
    
    private boolean meetsCriteria(Plugin plugin, TransportCriteria criteria) {
        if (criteria.requiresAnonymity() && !isAnonymous(plugin)) {
            return false;
        }
        if (criteria.getMaxLatency() < plugin.getMaxLatency()) {
            return false;
        }
        return true;
    }
    
    private int getEffectivePriority(Plugin plugin) {
        int basePriority = transportPriorities.getOrDefault(plugin.getId().getString(), 999);
        
        // 根据当前网络状况调整优先级
        if (isNetworkCongested() && plugin.getId().getString().equals("TCP")) {
            return basePriority + 10; // 降低TCP优先级
        }
        
        return basePriority;
    }
}

public class TransportCriteria {
    private final boolean requiresAnonymity;
    private final long maxLatency;
    private final boolean requiresReliability;
    
    public static TransportCriteria forPrivateMessage() {
        return new TransportCriteria(true, 30000, true);
    }
    
    public static TransportCriteria forFileTransfer() {
        return new TransportCriteria(false, Long.MAX_VALUE, true);
    }
    
    public static TransportCriteria forRealTimeChat() {
        return new TransportCriteria(false, 1000, false);
    }
}
```

## 🔧 连接管理机制

### 连接池实现

```java
public class ConnectionPool {
    private final Map<ContactId, Queue<DuplexTransportConnection>> connectionPools;
    private final Map<ContactId, AtomicInteger> connectionCounts;
    private final int maxConnectionsPerContact;
    
    public DuplexTransportConnection getConnection(ContactId contactId) {
        Queue<DuplexTransportConnection> pool = connectionPools.get(contactId);
        if (pool != null) {
            DuplexTransportConnection connection = pool.poll();
            if (connection != null && connection.isConnected()) {
                return connection;
            }
        }
        
        // 创建新连接
        return createNewConnection(contactId);
    }
    
    public void returnConnection(ContactId contactId, DuplexTransportConnection connection) {
        if (!connection.isConnected()) {
            return; // 不回收已断开的连接
        }
        
        Queue<DuplexTransportConnection> pool = connectionPools.computeIfAbsent(
            contactId, k -> new ConcurrentLinkedQueue<>()
        );
        
        if (pool.size() < maxConnectionsPerContact) {
            pool.offer(connection);
        } else {
            connection.close(); // 超出池大小限制，关闭连接
        }
    }
    
    public void cleanupIdleConnections() {
        long now = System.currentTimeMillis();
        
        for (Queue<DuplexTransportConnection> pool : connectionPools.values()) {
            pool.removeIf(conn -> {
                if (now - conn.getLastActivityTime() > IDLE_TIMEOUT) {
                    conn.close();
                    return true;
                }
                return false;
            });
        }
    }
}
```

## 📊 性能监控和优化

### 传输性能指标

```java
public class TransportMetrics {
    private final AtomicLong bytesSent = new AtomicLong();
    private final AtomicLong bytesReceived = new AtomicLong();
    private final AtomicLong connectionCount = new AtomicLong();
    private final AtomicLong failedConnections = new AtomicLong();
    
    private final Map<TransportId, TransportStats> transportStats = new ConcurrentHashMap<>();
    
    public void recordDataSent(TransportId transportId, int bytes) {
        bytesSent.addAndGet(bytes);
        getTransportStats(transportId).recordDataSent(bytes);
    }
    
    public void recordConnectionEstablished(TransportId transportId, long latency) {
        connectionCount.incrementAndGet();
        getTransportStats(transportId).recordConnection(latency);
    }
    
    public void recordConnectionFailed(TransportId transportId) {
        failedConnections.incrementAndGet();
        getTransportStats(transportId).recordFailure();
    }
    
    public TransportPerformanceReport generateReport() {
        return new TransportPerformanceReport(
            bytesSent.get(),
            bytesReceived.get(),
            connectionCount.get(),
            failedConnections.get(),
            new HashMap<>(transportStats)
        );
    }
}
```

---

**Briar的传输架构体现了现代分布式系统的设计精髓** 🌐

通过插件化设计、多协议支持、智能选择策略和性能优化，Briar构建了一个强大、灵活、高效的网络通信系统，为去中心化通信提供了坚实的网络基础。
