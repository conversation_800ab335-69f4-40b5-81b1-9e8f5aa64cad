# Briar数据库架构深度解析

## 概述

Briar项目采用了分层的数据库架构设计，通过抽象层将业务逻辑与具体的数据库实现分离。这种设计使得Briar能够支持多种数据库后端，同时保持代码的可维护性和可测试性。

## 架构层次

### 1. API层 (bramble-api)

API层定义了数据库操作的接口规范，主要包括：

```
bramble-api/
├── org.briarproject.bramble.api.db/
│   ├── Database.java              # 数据库接口
│   ├── DatabaseComponent.java     # 数据库组件接口
│   ├── Transaction.java           # 事务接口
│   ├── TransactionManager.java    # 事务管理器接口
│   └── Metadata.java             # 元数据接口
```

**核心接口说明：**

- `Database`: 定义了数据库的基本操作方法
- `DatabaseComponent`: 提供高级数据库操作，如联系人、消息管理
- `Transaction`: 表示数据库事务的抽象
- `TransactionManager`: 管理事务的生命周期

### 2. 核心实现层 (bramble-core)

核心实现层包含了数据库的具体实现和业务逻辑：

```
bramble-core/
├── org.briarproject.bramble.db/
│   ├── DatabaseComponentImpl.java     # 数据库组件实现
│   ├── TransactionManagerImpl.java    # 事务管理器实现
│   ├── JdbcDatabase.java             # JDBC数据库基类
│   ├── H2Database.java               # H2数据库实现
│   └── HyperSqlDatabase.java         # HyperSQL数据库实现
```

### 3. 平台适配层

不同平台有特定的数据库实现：

- **Java平台** (bramble-java): 使用H2数据库
- **Android平台** (bramble-android): 使用SQLite数据库

## 核心组件详解

### Database接口

```java
public interface Database {
    // 事务管理
    Transaction startTransaction(boolean readOnly) throws DbException;
    void commitTransaction(Transaction txn) throws DbException;
    void abortTransaction(Transaction txn);
    
    // 基本操作
    boolean containsContact(Transaction txn, ContactId c) throws DbException;
    boolean containsGroup(Transaction txn, GroupId g) throws DbException;
    boolean containsMessage(Transaction txn, MessageId m) throws DbException;
    
    // 数据访问
    Contact getContact(Transaction txn, ContactId c) throws DbException;
    Collection<Contact> getContacts(Transaction txn) throws DbException;
    Group getGroup(Transaction txn, GroupId g) throws DbException;
    
    // 数据修改
    void addContact(Transaction txn, Contact contact) throws DbException;
    void addGroup(Transaction txn, Group group) throws DbException;
    void addMessage(Transaction txn, Message message) throws DbException;
    
    // 删除操作
    void removeContact(Transaction txn, ContactId c) throws DbException;
    void removeGroup(Transaction txn, GroupId g) throws DbException;
    void removeMessage(Transaction txn, MessageId m) throws DbException;
}
```

### DatabaseComponent接口

```java
public interface DatabaseComponent {
    // 高级联系人操作
    void addContact(Contact contact, AuthorId remote, boolean verified) throws DbException;
    Collection<ContactId> getContacts() throws DbException;
    
    // 消息管理
    void addLocalMessage(Message message) throws DbException;
    Collection<MessageHeader> getMessageHeaders(GroupId g) throws DbException;
    
    // 群组管理
    void addGroup(Group group) throws DbException;
    Collection<Group> getGroups(ClientId clientId) throws DbException;
    
    // 设置管理
    Settings getSettings(String namespace) throws DbException;
    void mergeSettings(Settings settings, String namespace) throws DbException;
}
```

## 数据模型设计

### 核心表结构

#### 1. 设置表 (settings)
```sql
CREATE TABLE settings (
    namespace VARCHAR NOT NULL,
    settingKey VARCHAR NOT NULL,
    value VARCHAR NOT NULL,
    PRIMARY KEY (namespace, settingKey)
);
```

#### 2. 本地作者表 (localAuthors)
```sql
CREATE TABLE localAuthors (
    authorId BINARY(32) NOT NULL PRIMARY KEY,
    name VARCHAR NOT NULL,
    publicKey BINARY(32) NOT NULL,
    privateKey BINARY(32) NOT NULL,
    created BIGINT NOT NULL
);
```

#### 3. 联系人表 (contacts)
```sql
CREATE TABLE contacts (
    contactId BINARY(32) NOT NULL PRIMARY KEY,
    authorId BINARY(32) NOT NULL,
    name VARCHAR NOT NULL,
    alias VARCHAR,
    verified BOOLEAN NOT NULL DEFAULT FALSE,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    FOREIGN KEY (authorId) REFERENCES localAuthors (authorId)
);
```

#### 4. 群组表 (groups)
```sql
CREATE TABLE groups (
    groupId BINARY(32) NOT NULL PRIMARY KEY,
    clientId VARCHAR NOT NULL,
    majorVersion INT NOT NULL,
    descriptor BINARY NOT NULL
);
```

#### 5. 消息表 (messages)
```sql
CREATE TABLE messages (
    messageId BINARY(32) NOT NULL PRIMARY KEY,
    groupId BINARY(32) NOT NULL,
    timestamp BIGINT NOT NULL,
    state INT NOT NULL,
    shared BOOLEAN NOT NULL DEFAULT FALSE,
    raw BINARY NOT NULL,
    FOREIGN KEY (groupId) REFERENCES groups (groupId)
);
```

#### 6. 消息状态表 (statuses)
```sql
CREATE TABLE statuses (
    messageId BINARY(32) NOT NULL,
    contactId BINARY(32) NOT NULL,
    ack BOOLEAN NOT NULL DEFAULT FALSE,
    seen BOOLEAN NOT NULL DEFAULT FALSE,
    requested BOOLEAN NOT NULL DEFAULT FALSE,
    PRIMARY KEY (messageId, contactId),
    FOREIGN KEY (messageId) REFERENCES messages (messageId),
    FOREIGN KEY (contactId) REFERENCES contacts (contactId)
);
```

### 索引设计

```sql
-- 消息时间戳索引
CREATE INDEX idx_messages_timestamp ON messages (timestamp);

-- 群组消息索引
CREATE INDEX idx_messages_group ON messages (groupId, timestamp);

-- 消息状态索引
CREATE INDEX idx_statuses_contact ON statuses (contactId);

-- 联系人活跃状态索引
CREATE INDEX idx_contacts_active ON contacts (active);
```

## 事务管理机制

### 事务接口设计

```java
public interface Transaction {
    // 事务属性
    boolean isReadOnly();
    long getStartTime();
    
    // 连接管理
    Connection getConnection() throws DbException;
    
    // 事务状态
    void setSuccessful();
    boolean isSuccessful();
}
```

### 事务管理器实现

```java
public class TransactionManagerImpl implements TransactionManager {
    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private final Database database;
    
    @Override
    public void transaction(boolean readOnly, DbRunnable task) throws DbException {
        Transaction txn = startTransaction(readOnly);
        try {
            task.run(txn);
            commitTransaction(txn);
        } catch (Exception e) {
            abortTransaction(txn);
            throw new DbException(e);
        }
    }
    
    @Override
    public <R> R transactionWithResult(boolean readOnly, DbCallable<R> task) throws DbException {
        Transaction txn = startTransaction(readOnly);
        try {
            R result = task.call(txn);
            commitTransaction(txn);
            return result;
        } catch (Exception e) {
            abortTransaction(txn);
            throw new DbException(e);
        }
    }
    
    private Transaction startTransaction(boolean readOnly) {
        if (readOnly) {
            lock.readLock().lock();
        } else {
            lock.writeLock().lock();
        }
        return database.startTransaction(readOnly);
    }
}
```

## 数据库实现

### H2数据库实现

```java
public class H2Database extends JdbcDatabase {
    private static final String DRIVER_NAME = "org.h2.Driver";
    
    @Override
    protected Connection createConnection() throws SQLException {
        String url = "jdbc:h2:" + databasePath + ";CIPHER=AES";
        Properties props = new Properties();
        props.setProperty("user", "");
        props.setProperty("password", encryptionKey);
        
        return DriverManager.getConnection(url, props);
    }
    
    @Override
    protected void compactDatabase() throws DbException {
        // H2特定的数据库压缩操作
        executeStatement("SHUTDOWN COMPACT");
    }
}
```

### HyperSQL数据库实现

```java
public class HyperSqlDatabase extends JdbcDatabase {
    private static final String DRIVER_NAME = "org.hsqldb.jdbc.JDBCDriver";
    
    @Override
    protected Connection createConnection() throws SQLException {
        String url = "jdbc:hsqldb:file:" + databasePath + ";crypt_key=" + encryptionKey;
        return DriverManager.getConnection(url, "", "");
    }
    
    @Override
    protected void compactDatabase() throws DbException {
        // HyperSQL特定的数据库压缩操作
        executeStatement("CHECKPOINT DEFRAG");
    }
}
```

## 数据库安全机制

### 加密存储

```java
public class EncryptedDatabase {
    // 密钥派生
    private String deriveEncryptionKey(String password) {
        // 使用Scrypt进行密钥派生
        return scrypt.derive(password, salt, N, r, p);
    }
    
    // 加密连接
    private Connection createEncryptedConnection(String password) {
        String encryptionKey = deriveEncryptionKey(password);
        String url = "jdbc:h2:" + path + ";CIPHER=AES";
        
        Properties props = new Properties();
        props.setProperty("password", encryptionKey);
        
        return DriverManager.getConnection(url, props);
    }
}
```

### 访问控制

- **用户认证**: 基于密码的数据库访问控制
- **权限管理**: 通过数据库约束控制数据访问
- **审计日志**: 记录关键操作的审计信息

## 性能优化策略

### 1. 连接池管理

```java
public class DatabaseConnectionPool {
    private final BlockingQueue<Connection> pool;
    private final int maxConnections;
    
    public Connection getConnection() throws SQLException {
        Connection conn = pool.poll();
        if (conn == null || conn.isClosed()) {
            conn = createNewConnection();
        }
        return conn;
    }
    
    public void returnConnection(Connection conn) {
        if (conn != null && !conn.isClosed()) {
            pool.offer(conn);
        }
    }
}
```

### 2. 批量操作优化

```java
public void batchInsertMessages(List<Message> messages) throws DbException {
    String sql = "INSERT INTO messages (messageId, groupId, timestamp, raw) VALUES (?, ?, ?, ?)";
    
    try (PreparedStatement ps = connection.prepareStatement(sql)) {
        for (Message msg : messages) {
            ps.setBytes(1, msg.getId().getBytes());
            ps.setBytes(2, msg.getGroupId().getBytes());
            ps.setLong(3, msg.getTimestamp());
            ps.setBytes(4, msg.getRaw());
            ps.addBatch();
            
            if (++batchCount % 1000 == 0) {
                ps.executeBatch();
            }
        }
        ps.executeBatch();
    }
}
```

### 3. 查询优化

- **索引使用**: 为常用查询创建合适的索引
- **查询计划**: 使用EXPLAIN分析查询执行计划
- **结果集限制**: 使用LIMIT减少数据传输量
- **预编译语句**: 提高SQL执行效率

## 数据迁移管理

### 版本控制机制

```java
public class DatabaseMigration {
    private static final int CURRENT_SCHEMA_VERSION = 5;
    
    public void migrateIfNecessary() throws DbException {
        int dbVersion = getSchemaVersion();
        
        if (dbVersion < CURRENT_SCHEMA_VERSION) {
            performMigration(dbVersion, CURRENT_SCHEMA_VERSION);
        } else if (dbVersion > CURRENT_SCHEMA_VERSION) {
            throw new DbException("Database version too new");
        }
    }
    
    private void performMigration(int from, int to) throws DbException {
        for (int version = from + 1; version <= to; version++) {
            applyMigration(version);
        }
        updateSchemaVersion(to);
    }
}
```

### 迁移策略

- **增量迁移**: 只应用必要的模式变更
- **向前兼容**: 新版本能处理旧数据格式
- **回滚支持**: 迁移失败时的自动回滚机制
- **并发安全**: 防止多个进程同时执行迁移

---

**Briar的数据库架构体现了现代企业级系统的设计原则** 🗄️

通过分层设计、事务管理、安全机制和性能优化，Briar构建了一个可靠、高效、安全的数据存储系统，为去中心化通信提供了坚实的数据基础。
