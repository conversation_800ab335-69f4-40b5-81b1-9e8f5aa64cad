# Briar 语音消息功能实现

## 概述

本文档描述了为 Briar 项目实现的语音消息功能。该功能允许用户录制、发送和播放语音消息，为 Briar 的安全通信增加了新的交互方式。

## 功能特性

### 核心功能
- **语音录制**: 支持录制高质量的语音消息
- **语音播放**: 支持播放接收到的语音消息
- **进度控制**: 录制和播放过程中的实时进度显示
- **时长限制**: 最大录制时长为5分钟
- **文件大小限制**: 最大文件大小为2MB
- **权限管理**: 自动处理录音权限请求

### 技术特性
- **音频格式**: 支持 AAC、MP4、3GPP 格式
- **音频质量**: 44.1kHz 采样率，96kbps 比特率
- **UI集成**: 与现有的文本输入控件无缝集成
- **资源管理**: 自动清理临时文件和释放资源

## 实现架构

### 核心组件

#### 1. VoiceMessageRecorder
- **位置**: `briar-android/src/main/java/org/briarproject/briar/android/voice/VoiceMessageRecorder.java`
- **功能**: 负责语音录制的核心逻辑
- **特性**:
  - 权限检查
  - 录制控制（开始、停止、取消）
  - 进度跟踪
  - 错误处理
  - 最大时长控制

#### 2. VoiceMessagePlayer
- **位置**: `briar-android/src/main/java/org/briarproject/briar/android/voice/VoiceMessagePlayer.java`
- **功能**: 负责语音播放的核心逻辑
- **特性**:
  - 音频准备和播放
  - 播放控制（播放、暂停、停止、跳转）
  - 进度跟踪
  - 错误处理

#### 3. VoiceMessageView
- **位置**: `briar-android/src/main/java/org/briarproject/briar/android/view/VoiceMessageView.java`
- **功能**: 语音消息的UI显示组件
- **特性**:
  - 播放控制按钮
  - 进度条显示
  - 时长显示
  - 加载状态指示

#### 4. VoiceAttachmentController
- **位置**: `briar-android/src/main/java/org/briarproject/briar/android/view/VoiceAttachmentController.java`
- **功能**: 扩展现有的附件控制器以支持语音消息
- **特性**:
  - 与文本输入控件集成
  - 录制状态管理
  - 发送逻辑集成

### UI组件

#### 1. 语音消息布局
- **位置**: `briar-android/src/main/res/layout/view_voice_message.xml`
- **内容**: 播放按钮、进度条、时长显示

#### 2. 图标资源
- **播放图标**: `ic_play_arrow_24dp.xml`
- **暂停图标**: `ic_pause_24dp.xml`
- **麦克风图标**: `ic_mic_24dp.xml`

#### 3. 字符串资源
- **位置**: `briar-android/src/main/res/values/strings.xml`
- **内容**: 语音消息相关的所有用户界面文本

### 常量定义

#### MediaConstants 扩展
- **位置**: `briar-api/src/main/java/org/briarproject/briar/api/attachment/MediaConstants.java`
- **新增常量**:
  - `MAX_AUDIO_SIZE`: 最大音频文件大小 (2MB)
  - `MAX_VOICE_MESSAGE_DURATION_MS`: 最大录制时长 (5分钟)
  - 支持的音频内容类型

## 集成点

### 1. CompositeSendButton 扩展
- 添加了语音按钮支持
- 更新了按钮状态管理逻辑
- 支持语音、图片、发送按钮的动态切换

### 2. TextAttachmentController 扩展
- VoiceAttachmentController 继承并扩展了现有功能
- 保持了与现有图片附件功能的兼容性
- 统一的附件处理流程

## 测试覆盖

### 单元测试
1. **VoiceMessageRecorderTest**
   - 权限检查测试
   - 录制状态测试
   - 错误处理测试
   - 资源清理测试

2. **VoiceMessagePlayerTest**
   - 播放状态测试
   - 文件准备测试
   - 错误处理测试
   - 资源清理测试

### 测试结果
- 所有测试通过 ✅
- 测试覆盖了核心功能和边界情况
- 使用 Robolectric 框架进行 Android 组件测试

## 使用流程

### 录制语音消息
1. 用户点击麦克风按钮
2. 系统检查录音权限
3. 开始录制，显示录制进度
4. 用户可以停止录制或达到最大时长自动停止
5. 录制完成后可以添加文字说明
6. 点击发送按钮发送语音消息

### 播放语音消息
1. 接收到语音消息后显示语音消息控件
2. 点击播放按钮开始播放
3. 显示播放进度和剩余时间
4. 支持拖拽进度条跳转
5. 播放完成或用户停止

## 安全考虑

### 权限管理
- 录音权限的动态请求和检查
- 权限被拒绝时的优雅处理

### 文件安全
- 临时文件的安全存储
- 录制取消时的文件清理
- 发送完成后的文件清理

### 资源管理
- MediaRecorder 和 MediaPlayer 的正确释放
- 内存泄漏防护
- 异常情况下的资源清理

## 配置参数

### 音频质量设置
```java
// 音频源
MediaRecorder.AudioSource.MIC

// 输出格式
MediaRecorder.OutputFormat.AAC_ADTS

// 音频编码器
MediaRecorder.AudioEncoder.AAC

// 采样率
44100 Hz

// 比特率
96000 bps
```

### 限制参数
```java
// 最大文件大小
MAX_AUDIO_SIZE = 2 * 1024 * 1024; // 2MB

// 最大录制时长
MAX_VOICE_MESSAGE_DURATION_MS = 5 * 60 * 1000; // 5分钟
```

## 未来改进

### 可能的增强功能
1. **音频波形显示**: 在录制和播放时显示音频波形
2. **音频压缩**: 实现更高效的音频压缩算法
3. **语音转文字**: 集成语音识别功能
4. **音频滤镜**: 添加降噪、音量调节等功能
5. **多语言支持**: 扩展更多语言的界面文本

### 性能优化
1. **异步处理**: 将音频处理移到后台线程
2. **缓存机制**: 实现音频文件的智能缓存
3. **内存优化**: 减少音频处理时的内存占用

## 结论

语音消息功能的实现为 Briar 用户提供了更丰富的通信方式，同时保持了 Briar 一贯的安全性和隐私保护特性。该功能与现有架构良好集成，具有良好的可扩展性和维护性。

通过完善的测试覆盖和错误处理，确保了功能的稳定性和可靠性。未来可以根据用户反馈和需求进一步优化和扩展功能。
