# Briar同步和消息机制详解

## 🔄 同步机制概述

Briar采用了一套精心设计的同步协议，确保分布式网络中的消息能够可靠、高效地在节点间传播，同时保持数据一致性和处理网络分区。

### 核心设计原则
- **最终一致性**：所有节点最终会达到相同的状态
- **增量同步**：只传输差异数据，减少网络开销
- **冲突解决**：自动处理并发更新的冲突
- **离线支持**：支持节点离线后重新同步

## 🏗️ 同步架构

```mermaid
graph TB
    subgraph "同步层架构"
        SM[SyncManager]
        SSF[SyncSessionFactory]
        
        subgraph "会话类型"
            ISS[IncomingSession]
            DSO[DuplexOutgoingSession]
            SSO[SimplexOutgoingSession]
            ESO[EagerSimplexOutgoingSession]
        end
        
        subgraph "记录处理"
            SRR[SyncRecordReader]
            SRW[SyncRecordWriter]
            RF[RecordFactory]
        end
        
        subgraph "消息管理"
            MF[MessageFactory]
            MV[MessageValidator]
            MS[MessageStore]
        end
    end
    
    subgraph "数据库层"
        DB[(Database)]
        TXN[Transaction]
    end
    
    subgraph "网络层"
        CONN[Connection]
        TRANS[Transport]
    end
    
    SM --> SSF
    SSF --> ISS
    SSF --> DSO
    SSF --> SSO
    SSF --> ESO
    
    ISS --> SRR
    DSO --> SRW
    SSO --> SRW
    ESO --> SRW
    
    SRR --> RF
    SRW --> RF
    
    RF --> MF
    MF --> MV
    MV --> MS
    MS --> DB
    
    ISS --> CONN
    DSO --> CONN
    CONN --> TRANS
```

## 📡 同步协议流程

### 连接建立和版本协商

#### 1. 协议版本交换
```java
// 支持的协议版本
public interface SyncConstants {
    byte PROTOCOL_VERSION = 0;
    List<Byte> SUPPORTED_VERSIONS = singletonList(PROTOCOL_VERSION);
}

// 版本协商过程
public void negotiateVersion() {
    // 发送支持的版本列表
    recordWriter.writeVersions(new Versions(SUPPORTED_VERSIONS));
    
    // 接收对方的版本列表
    Versions theirVersions = recordReader.readVersions();
    
    // 选择最高的共同支持版本
    byte negotiatedVersion = selectHighestCommonVersion(
        SUPPORTED_VERSIONS, theirVersions.getSupportedVersions());
}
```

#### 2. 优先级交换
```java
// 避免冗余连接的优先级机制
public class Priority {
    private final byte[] nonce;  // 16字节随机数
    
    public Priority() {
        this.nonce = new byte[PRIORITY_NONCE_BYTES];
        secureRandom.nextBytes(nonce);
    }
    
    // 比较优先级，决定保留哪个连接
    public int compareTo(Priority other) {
        return ByteUtils.compareUnsigned(this.nonce, other.nonce);
    }
}
```

### 同步记录类型

#### 1. Offer记录 - 提供可发送的消息
```java
public class Offer {
    private final Collection<MessageId> messageIds;
    
    // 生成Offer记录
    public Offer generateOffer(ContactId contactId, int maxMessageIds, long maxLatency) {
        return db.transactionWithResult(false, txn -> {
            Collection<MessageId> ids = db.getMessagesToOffer(txn, contactId, maxMessageIds, maxLatency);
            return new Offer(ids);
        });
    }
}
```

#### 2. Request记录 - 请求特定消息
```java
public class Request {
    private final Collection<MessageId> messageIds;
    
    // 处理Offer并生成Request
    public Request processOffer(Offer offer) {
        Collection<MessageId> requestedIds = new ArrayList<>();
        for (MessageId id : offer.getMessageIds()) {
            if (!db.containsMessage(id)) {
                requestedIds.add(id);
            }
        }
        return new Request(requestedIds);
    }
}
```

#### 3. Message记录 - 实际消息数据
```java
public class Message {
    private final MessageId id;
    private final GroupId groupId;
    private final long timestamp;
    private final byte[] body;
    
    // 消息ID计算
    private MessageId calculateMessageId(GroupId groupId, long timestamp, byte[] body) {
        // 计算消息体的哈希
        byte[] rootHash = crypto.hash(BLOCK_LABEL, FORMAT_VERSION_BYTES, body);
        
        // 组合组ID、时间戳和哈希计算最终ID
        byte[] timeBytes = new byte[8];
        ByteUtils.writeUint64(timestamp, timeBytes, 0);
        byte[] idHash = crypto.hash(ID_LABEL, FORMAT_VERSION_BYTES,
                groupId.getBytes(), timeBytes, rootHash);
        
        return new MessageId(idHash);
    }
}
```

#### 4. Ack记录 - 确认接收
```java
public class Ack {
    private final Collection<MessageId> messageIds;
    
    // 生成确认记录
    public Ack generateAck(ContactId contactId, int maxMessageIds) {
        return db.transactionWithResult(false, txn -> {
            Collection<MessageId> ackedIds = db.getMessagesToAck(txn, contactId, maxMessageIds);
            if (!ackedIds.isEmpty()) {
                db.markMessagesAsAcked(txn, contactId, ackedIds);
            }
            return ackedIds.isEmpty() ? null : new Ack(ackedIds);
        });
    }
}
```

## 🔄 同步会话类型

### 1. 双工会话 (DuplexOutgoingSession)

双工会话支持双向通信，可以同时发送和接收数据。

```java
public class DuplexOutgoingSession implements SyncSession {
    @Override
    public void run() throws IOException {
        // 发送协议版本
        recordWriter.writeVersions(new Versions(SUPPORTED_VERSIONS));
        
        // 发送连接优先级
        if (priority != null) recordWriter.writePriority(priority);
        
        // 启动各种记录生成任务
        generateAck();      // 生成确认记录
        generateBatch();    // 生成消息批次
        generateOffer();    // 生成提供记录
        generateRequest();  // 生成请求记录
        
        // 主循环：处理记录直到中断
        while (!interrupted) {
            // 等待并处理下一个记录
            SyncRecord record = writerTasks.poll(keepaliveWait, MILLISECONDS);
            
            if (record != null) {
                record.write(recordWriter);
                dataToFlush = true;
            } else {
                // 发送保活记录
                recordWriter.writeKeepalive();
                nextKeepalive = now + maxIdleTime;
            }
            
            // 定期刷新输出
            if (dataToFlush && shouldFlush(now)) {
                recordWriter.flush();
                dataToFlush = false;
            }
        }
    }
}
```

### 2. 单工会话 (SimplexOutgoingSession)

单工会话只支持单向发送数据，适用于邮箱等异步传输。

```java
public class SimplexOutgoingSession implements SyncSession {
    @Override
    public void run() throws IOException {
        try {
            sendAcks();      // 发送所有待确认
            sendMessages();  // 发送所有待发送消息
        } finally {
            streamWriter.dispose(false, true);
        }
    }
    
    void sendMessages() throws DbException, IOException {
        while (!isInterrupted()) {
            if (!generateAndSendBatch()) break;
        }
    }
    
    private boolean generateAndSendBatch() throws DbException, IOException {
        Collection<Message> batch = db.transactionWithNullableResult(false, txn ->
                db.generateBatch(txn, contactId, BATCH_CAPACITY, maxLatency));
        
        if (batch == null) return false;
        
        for (Message m : batch) {
            recordWriter.writeMessage(m);
        }
        return true;
    }
}
```

### 3. 急切单工会话 (EagerSimplexOutgoingSession)

急切模式会立即发送所有未确认的消息，不考虑重传时间。

```java
public class EagerSimplexOutgoingSession extends SimplexOutgoingSession {
    @Override
    void sendMessages() throws DbException, IOException {
        // 加载所有未确认的消息ID
        for (MessageId id : loadUnackedMessageIdsToSend()) {
            if (isInterrupted()) break;
            
            Message message = db.transactionWithNullableResult(false, txn ->
                    db.getMessageToSend(txn, contactId, id, maxLatency, true));
            
            if (message != null) {
                recordWriter.writeMessage(message);
            }
        }
    }
}
```

## 📨 消息处理机制

### 消息生命周期

#### 1. 消息创建
```java
public class MessageFactoryImpl implements MessageFactory {
    @Override
    public Message createMessage(GroupId groupId, long timestamp, byte[] body) {
        // 验证消息体长度
        if (body.length == 0 || body.length > MAX_MESSAGE_BODY_LENGTH) {
            throw new IllegalArgumentException();
        }
        
        // 计算消息ID
        MessageId id = getMessageId(groupId, timestamp, body);
        
        return new Message(id, groupId, timestamp, body);
    }
}
```

#### 2. 消息存储
```java
// 消息存储到数据库
public void storeMessage(Message message, MessageState state) {
    db.transaction(false, txn -> {
        // 存储消息内容
        db.addMessage(txn, message, state, true, null);
        
        // 更新组状态
        db.setGroupVisibility(txn, message.getGroupId(), VISIBLE);
        
        // 触发消息事件
        eventBus.broadcast(new MessageAddedEvent(message, contactId));
    });
}
```

#### 3. 消息同步状态
```java
public enum MessageState {
    UNKNOWN,     // 未知状态
    PENDING,     // 等待发送
    SENT,        // 已发送
    DELIVERED,   // 已送达
    INVALID      // 无效消息
}

// 状态转换
public void updateMessageState(MessageId id, MessageState newState) {
    db.transaction(false, txn -> {
        MessageState currentState = db.getMessageState(txn, id);
        
        // 验证状态转换的合法性
        if (isValidStateTransition(currentState, newState)) {
            db.setMessageState(txn, id, newState);
            
            // 触发状态变更事件
            eventBus.broadcast(new MessageStateChangedEvent(id, currentState, newState));
        }
    });
}
```

### 消息验证机制

#### 1. 结构验证
```java
public class MessageValidator {
    public boolean validateMessage(Message message) {
        // 检查消息ID是否正确
        MessageId expectedId = calculateMessageId(
            message.getGroupId(), 
            message.getTimestamp(), 
            message.getBody()
        );
        
        if (!message.getId().equals(expectedId)) {
            return false;
        }
        
        // 检查时间戳合理性
        long now = clock.currentTimeMillis();
        if (message.getTimestamp() > now + MAX_CLOCK_SKEW) {
            return false;
        }
        
        // 检查消息体长度
        if (message.getBody().length > MAX_MESSAGE_BODY_LENGTH) {
            return false;
        }
        
        return true;
    }
}
```

#### 2. 内容验证
```java
// 应用层消息验证
public interface MessageValidator {
    MessageContext validateMessage(Message message, Group group) throws FormatException;
}

// 具体实现示例
public class PrivateMessageValidator implements MessageValidator {
    @Override
    public MessageContext validateMessage(Message message, Group group) throws FormatException {
        // 解析消息内容
        BdfList body = clientHelper.parseAndValidateAuthor(message);
        
        // 验证消息格式
        String text = body.getString(0);
        long timestamp = body.getLong(1);
        
        // 验证时间戳
        if (timestamp != message.getTimestamp()) {
            throw new FormatException();
        }
        
        return new MessageContext(message.getId());
    }
}
```

## 🔄 冲突解决机制

### 时间戳排序
```java
public class ConflictResolver {
    // 基于时间戳的消息排序
    public List<Message> resolveConflicts(List<Message> conflictingMessages) {
        return conflictingMessages.stream()
                .sorted(Comparator.comparing(Message::getTimestamp)
                        .thenComparing(m -> m.getId().getBytes(), ByteUtils::compareUnsigned))
                .collect(Collectors.toList());
    }
}
```

### 向量时钟 (Vector Clock)
```java
public class VectorClock {
    private final Map<ContactId, Long> clocks = new HashMap<>();
    
    public void increment(ContactId nodeId) {
        clocks.put(nodeId, clocks.getOrDefault(nodeId, 0L) + 1);
    }
    
    public boolean happensBefore(VectorClock other) {
        boolean strictlyLess = false;
        
        for (ContactId nodeId : getAllNodeIds()) {
            long thisClock = this.clocks.getOrDefault(nodeId, 0L);
            long otherClock = other.clocks.getOrDefault(nodeId, 0L);
            
            if (thisClock > otherClock) {
                return false;  // 不是happens-before关系
            }
            if (thisClock < otherClock) {
                strictlyLess = true;
            }
        }
        
        return strictlyLess;
    }
}
```

## 📊 性能优化

### 批量处理
```java
// 批量发送消息
private static final int BATCH_CAPACITY = 16;

public Collection<Message> generateBatch(ContactId contactId, int capacity, long maxLatency) {
    return db.transactionWithResult(false, txn -> {
        Collection<Message> batch = new ArrayList<>();
        
        // 获取待发送的消息
        Collection<MessageId> pendingIds = db.getMessagesToSend(txn, contactId, capacity, maxLatency);
        
        for (MessageId id : pendingIds) {
            Message message = db.getMessage(txn, id);
            if (message != null) {
                batch.add(message);
            }
        }
        
        return batch;
    });
}
```

### 增量同步
```java
// 只同步差异数据
public Offer generateOffer(ContactId contactId, int maxMessageIds, long maxLatency) {
    return db.transactionWithResult(false, txn -> {
        // 获取对方没有的消息
        Collection<MessageId> unseenMessages = db.getUnseenMessages(txn, contactId, maxMessageIds);
        
        // 过滤掉延迟过高的消息
        Collection<MessageId> offerIds = unseenMessages.stream()
                .filter(id -> getMessageLatency(id) <= maxLatency)
                .collect(Collectors.toList());
        
        return new Offer(offerIds);
    });
}
```

### 连接复用
```java
// 在单个连接上复用多个同步会话
public class ConnectionManager {
    public void multiplexSessions(DuplexTransportConnection connection) {
        // 为不同的联系人创建多个会话
        for (ContactId contactId : getActiveContacts()) {
            SyncSession session = sessionFactory.createDuplexOutgoingSession(
                contactId, transportId, maxLatency, maxIdleTime, 
                connection.getStreamWriter(), priority);
            
            // 并发执行会话
            executor.execute(session);
        }
    }
}
```

这套同步和消息机制确保了Briar网络中的数据能够可靠、高效地传播，同时保持了良好的性能和一致性。
