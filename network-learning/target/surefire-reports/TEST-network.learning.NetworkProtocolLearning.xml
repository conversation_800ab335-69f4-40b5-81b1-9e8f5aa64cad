<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="network.learning.NetworkProtocolLearning" time="0.025" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/network-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/network-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/network-learning/target/surefire/surefirebooter-20250704113302098_3.jar /Volumes/ExtendData/Code/github/briar/network-learning/target/surefire 2025-07-04T11-33-02_020-jvmRun1 surefire-20250704113302098_1tmp surefire_0-20250704113302098_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="NetworkProtocolLearning"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/network-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/network-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/network-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/network-learning/target/surefire/surefirebooter-20250704113302098_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/network-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testConnectionKeepalive" classname="network.learning.NetworkProtocolLearning" time="0.004">
    <system-out><![CDATA[💓 开始连接保活机制测试
==================================================
🔗 连接已建立，开始保活测试

💓 发送心跳包:
  💓 心跳 1: 时间戳 1751599982222
  💓 心跳 2: 时间戳 1751599983222
  💓 心跳 3: 时间戳 1751599984222
  💓 心跳 4: 时间戳 1751599985222
  💓 心跳 5: 时间戳 1751599986222

📥 处理接收到的心跳:
  ✅ 心跳 1 处理成功
  ✅ 心跳 2 处理成功
  ✅ 心跳 3 处理成功
  ✅ 心跳 4 处理成功
  ✅ 心跳 5 处理成功

⏰ 测试连接超时检测:
  当前时间: 1751599982223
  最后活跃时间: 1751599986222
  空闲时间: -3999 ms
  ✅ 连接状态: 活跃

⚠️ 模拟长时间无心跳:
  ⚠️ 连接状态: 超时
✅ 连接保活机制测试完成

]]></system-out>
  </testcase>
  <testcase name="testProtocolVersionCompatibility" classname="network.learning.NetworkProtocolLearning" time="0.001">
    <system-out><![CDATA[🔄 开始协议版本兼容性测试
==================================================
📋 支持的协议版本:
  - V1 (值: 1)
  - V2 (值: 2)
  - V3 (值: 3)
  - V4 (值: 4)

🤝 测试版本协商:
  客户端支持: V4, V3
  服务器支持: V3, V2
  ✅ 协商结果: V3

❌ 测试不兼容情况:
  客户端支持: V4
  服务器支持: V2
  ❌ 协商结果: 无兼容版本
✅ 协议版本兼容性测试完成

]]></system-out>
  </testcase>
  <testcase name="testConnectionHandshake" classname="network.learning.NetworkProtocolLearning" time="0">
    <system-out><![CDATA[🤝 开始连接建立握手测试
==================================================
👥 模拟Alice和Bob的握手过程:
  📤 Alice发送握手请求
     Alice握手帧大小: 36 字节
     Alice协议版本: V1
  📥 Bob接收握手请求并响应
     Bob握手帧大小: 34 字节
     Bob协议版本: V1
  ✅ Alice确认Bob的握手
  🎉 握手完成，连接已建立
     Alice状态: ESTABLISHED
     Bob状态: ESTABLISHED
✅ 连接建立握手测试完成

]]></system-out>
  </testcase>
  <testcase name="testReliableDataTransmission" classname="network.learning.NetworkProtocolLearning" time="0.002">
    <system-out><![CDATA[📡 开始可靠数据传输测试
==================================================
🔗 连接已建立，开始数据传输测试

📤 发送方传输数据:
  📦 发送帧 1: 第一条消息：Hello Briar!...
     序列号: 1
     数据长度: 30 字节
  📦 发送帧 2: 第二条消息：这是一个较长的消息，用来测试...
     序列号: 2
     数据长度: 81 字节
  📦 发送帧 3: 第三条消息：包含特殊字符 @#$%^&*...
     序列号: 3
     数据长度: 48 字节
  📦 发送帧 4: 第四条消息：最后一条测试消息。...
     序列号: 4
     数据长度: 45 字节

📥 接收方处理数据:
  📨 接收帧 1: 第一条消息：Hello Briar!...
     ✅ 确认帧已发送和处理
  📨 接收帧 2: 第二条消息：这是一个较长的消息，用来测试...
     ✅ 确认帧已发送和处理
  📨 接收帧 3: 第三条消息：包含特殊字符 @#$%^&*...
     ✅ 确认帧已发送和处理
  📨 接收帧 4: 第四条消息：最后一条测试消息。...
     ✅ 确认帧已发送和处理

🔍 验证传输结果:
  ✅ 消息 1 传输正确
  ✅ 消息 2 传输正确
  ✅ 消息 3 传输正确
  ✅ 消息 4 传输正确
✅ 可靠数据传输测试完成

]]></system-out>
  </testcase>
  <testcase name="testProtocolFrameStructure" classname="network.learning.NetworkProtocolLearning" time="0.001">
    <system-out><![CDATA[📦 开始协议帧结构测试
==================================================
🔧 创建不同类型的协议帧:
  ✅ 握手帧: 36 字节
     版本: V4
     公钥长度: 16
  ✅ 数据帧: 43 字节
     序列号: 1
     数据长度: 30
     是否最后帧: false
  ✅ 确认帧: 9 字节
     确认序列号: 1
     确认状态: 成功
  ✅ 心跳帧: 12 字节
     时间戳: 1751599982226

🔍 测试帧的序列化和反序列化:
  ✅ 握手帧序列化/反序列化成功
  ✅ 数据帧序列化/反序列化成功
✅ 协议帧结构测试完成

]]></system-out>
  </testcase>
</testsuite>