<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="network.learning.TransportPluginLearning" time="0.138" tests="5" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Volumes/ExtendData/Code/github/briar/network-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/network-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Volumes/ExtendData/Code/github/briar/network-learning/target/surefire/surefirebooter-20250704141844512_3.jar /Volumes/ExtendData/Code/github/briar/network-learning/target/surefire 2025-07-04T14-18-44_440-jvmRun1 surefire-20250704141844512_1tmp surefire_0-20250704141844512_2tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="TransportPluginLearning"/>
    <property name="surefire.test.class.path" value="/Volumes/ExtendData/Code/github/briar/network-learning/target/test-classes:/Volumes/ExtendData/Code/github/briar/network-learning/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/mockito/mockito-core/3.12.4/mockito-core-3.12.4.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.11.13/byte-buddy-1.11.13.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.11.13/byte-buddy-agent-1.11.13.jar:/Users/<USER>/.m2/repository/org/objenesis/objenesis/3.2/objenesis-3.2.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-simple/1.7.36/slf4j-simple-1.7.36.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.36/slf4j-api-1.7.36.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.13.4/jackson-databind-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.13.4/jackson-annotations-2.13.4.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.13.4/jackson-core-2.13.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="zh"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-04-15"/>
    <property name="java.home" value="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Volumes/ExtendData/Code/github/briar/network-learning"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="/Volumes/ExtendData/Code/github/briar/network-learning/target/surefire/surefirebooter-20250704141844512_3.jar"/>
    <property name="user.script" value="Hans"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="17.0.15+9-LTS-241"/>
    <property name="user.name" value="wangyangyang"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="15.5"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="/var/folders/bt/9ksb4y4n41q61t6ptvv12cx80000gn/T/"/>
    <property name="java.version" value="17.0.15"/>
    <property name="user.dir" value="/Volumes/ExtendData/Code/github/briar/network-learning"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.15+9-LTS-241"/>
    <property name="java.specification.maintenance.version" value="1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testSimplexPluginOperations" classname="network.learning.TransportPluginLearning" time="0.018">
    <system-out><![CDATA[📁 开始单工插件操作测试
==================================================
📝 测试文件写入:
  ✅ 数据写入成功
  文件路径: /tmp/briar_test_write.dat
  数据长度: 44 字节

📖 测试文件读取:
  ✅ 数据读取成功
  读取长度: 58 字节
  读取内容: Mock file data from /tmp/briar_test_read.dat
Line ...
✅ 单工插件操作测试完成

]]></system-out>
  </testcase>
  <testcase name="testTransportPriorityAndSelection" classname="network.learning.TransportPluginLearning" time="0.038">
    <system-out><![CDATA[🎯 开始传输优先级和选择测试
==================================================
🏆 传输优先级设置:
  1. TOR (匿名性优先)
  2. TCP (性能优先)
  3. BLUETOOTH (近距离)

🎭 场景1: 所有传输都可用
  ✅ 选择结果: TOR

🎭 场景2: Tor不可用
  ✅ 选择结果: TCP

🎭 场景3: 只有蓝牙可用
  ✅ 选择结果: BLUETOOTH

🎭 场景4: 无可用传输
  ✅ 选择结果: 无可用传输
✅ 传输优先级和选择测试完成

]]></system-out>
  </testcase>
  <testcase name="testDuplexPluginConnection" classname="network.learning.TransportPluginLearning" time="0.014">
    <system-out><![CDATA[🔗 开始双工插件连接测试
==================================================
🌐 尝试创建TCP连接:
  地址: 192.168.1.100
  端口: 8080
✅ TCP连接创建成功
  连接ID: conn_1751609924690_133
  插件: TCP

📡 测试连接功能:
  ✅ 数据发送成功: Hello, Briar Network!
  ✅ 数据接收成功: Hello, Briar Network!
  ✅ 连接已关闭
✅ 双工插件连接测试完成

]]></system-out>
  </testcase>
  <testcase name="testConnectionManagement" classname="network.learning.TransportPluginLearning" time="0.027">
    <system-out><![CDATA[🔗 开始连接管理测试
==================================================
🌐 创建多个连接:
  ✅ Alice (TCP): conn_1751609924716_505
  ✅ Alice (Tor): conn_1751609924716_235
  ✅ Bob (TCP): conn_1751609924716_627

📊 连接状态统计:
  已连接联系人数: 2
  总连接数: 3
  Alice的连接数: 2
  Bob的连接数: 1

🔌 关闭连接:
  ✅ Alice的TCP连接已关闭
  ✅ Alice的Tor连接已关闭
  ✅ Bob的TCP连接已关闭
✅ 连接管理测试完成

]]></system-out>
  </testcase>
  <testcase name="testPluginLifecycle" classname="network.learning.TransportPluginLearning" time="0.025">
    <system-out><![CDATA[🔌 开始插件生命周期测试
==================================================
📋 已注册插件:
  - TCP: INACTIVE
  - TOR: INACTIVE

🚀 启动插件:
  ✅ TCP: ACTIVE
  ✅ TOR: ACTIVE

🛑 停止插件:
  ✅ TCP: INACTIVE
  ✅ TOR: INACTIVE
✅ 插件生命周期测试完成

]]></system-out>
  </testcase>
</testsuite>